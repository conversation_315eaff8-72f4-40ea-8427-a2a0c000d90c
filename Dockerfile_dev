FROM debian:bookworm

# Install necessary dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-venv \
    python3-pip \
    python3-dev \
    python3-full \
    git \
    build-essential \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create a virtual environment for installing uv
RUN python3 -m venv /uv_venv
RUN /uv_venv/bin/pip install uv

# Create virtual environment with python 3
RUN /uv_venv/bin/uv venv --python python3 /.venv

# Clone repo and pin commit
WORKDIR /home
RUN git clone https://github.com/Cinnamon/kotaemon.git
RUN cd kotaemon && git checkout a3e2e20735c2e95682f9d56226451453cdad7e04

# Create virtual environment using uv and Python 3.10
WORKDIR /home/<USER>
RUN /.venv/bin/python -m ensurepip
RUN /.venv/bin/python -m pip install --upgrade pip
RUN /.venv/bin/pip install pre-commit
RUN /.venv/bin/pip install torch --index-url https://download.pytorch.org/whl/cpu

# Install the project (editable mode)
WORKDIR /home/<USER>/libs/kotaemon
RUN /.venv/bin/python -m pip install -U --upgrade-strategy eager -e .[all]

# Install pytest explicitly
RUN /.venv/bin/pip install pytest pytest-cov

# Run tests once to pre-cache dependencies
RUN /.venv/bin/pre-commit clean || true
RUN /.venv/bin/pre-commit run --all-files || true
RUN cd /home/<USER>/libs/kotaemon && /.venv/bin/pytest || true

WORKDIR /home/<USER>

ENV PYTHONPATH=/home/<USER>
ENV PATH="/.venv/bin:$PATH"

# Automatically activate virtualenv on container shell startup
RUN echo 'source /.venv/bin/activate' >> /etc/profile

# Install bash for better interactive experience
RUN apt-get update && apt-get install -y bash && apt-get clean

# Make bash the default shell
SHELL ["/bin/bash", "-c"]

# Default command
CMD ["bash"]