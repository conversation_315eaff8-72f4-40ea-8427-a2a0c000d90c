name: Build and Push Docker Image

on:
  release:
    types:
      - created

  push:
    tags:
      - "v[0-9]+.[0-9]+.[0-9]+"

  workflow_dispatch:

env:
  REGISTRY: ghcr.io

jobs:
  build:
    name: Build and push container
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
      attestations: write
      id-token: write
    strategy:
      matrix:
        target:
          - lite
          - full
          - ollama
      # The maximum number of jobs that can run simultaneously
      max-parallel: 1
    steps:
      - name: Free Disk Space (Ubuntu)
        uses: jlumbroso/free-disk-space@main
        with:
          # this might remove tools that are actually needed,
          # if set to "true" but frees about 6 GB
          tool-cache: true

          # all of these default to true, but feel free to set to
          # "false" if necessary for your workflow
          android: true
          dotnet: true
          haskell: true
          large-packages: true
          docker-images: true
          swap-storage: true

      - name: Set repository and image name
        run: |
          echo "FULL_IMAGE_NAME=${{ env.REGISTRY }}/${IMAGE_NAME,,}" >>${GITHUB_ENV}
        env:
          IMAGE_NAME: "${{ github.repository }}"

      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          image: tonistiigi/binfmt:latest
          platforms: arm64,arm

      - name: Set up Docker Buildx
        id: buildx
        uses: docker/setup-buildx-action@v3

      - name: Set up Docker meta
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.FULL_IMAGE_NAME }}
          tags: |
            # branch
            type=ref,event=branch,suffix=-${{ matrix.target }}
            # semver with suffix for lite/full targets
            type=semver,pattern={{version}},suffix=-${{ matrix.target }}
            # latest tag with suffix for lite/full targets
            type=raw,value=latest,enable=${{ startsWith(github.ref, 'refs/tags/') && !contains(github.ref, 'pre') }},suffix=-${{ matrix.target }}
          flavor: |
            # This is disabled here so we can use the raw form above
            latest=false
            # Suffix is not used here since there's no way to disable it above

      - name: Log in to the Container registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build docker image
        uses: docker/build-push-action@v6
        with:
          file: Dockerfile
          context: .
          push: true
          platforms: linux/amd64, linux/arm64
          tags: |
            ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          target: ${{ matrix.target }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
