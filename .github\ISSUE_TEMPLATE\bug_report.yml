name: "Bug Report"
description: Report something that is not working as expected
title: "[BUG] "
labels: ["bug"]
body:
  - type: markdown
    attributes:
      value: |
        *Please fill this form with as much information as possible.*
  - type: textarea
    id: description
    attributes:
      label: "Description"
      description: Please enter an explicit description of your issue
      placeholder: Short and explicit description of your incident...
    validations:
      required: true
  - type: textarea
    id: reprod
    attributes:
      label: "Reproduction steps"
      description: Please enter an explicit description of your issue
      value: |
        1. Go to '...'
        2. Click on '....'
        3. Scroll down to '....'
        4. See error
      render: bash
    validations:
      required: true
  - type: textarea
    id: screenshot
    attributes:
      label: "Screenshots"
      description: If applicable, add screenshots to help explain your problem.
      value: |
        ![DESCRIPTION](LINK.png)
      render: bash
    validations:
      required: false
  - type: textarea
    id: logs
    attributes:
      label: "Logs"
      description: Please copy and paste any relevant log output. This will be automatically formatted into code, so no need for backticks.
      render: bash
    validations:
      required: false
  - type: dropdown
    id: browsers
    attributes:
      label: "Browsers"
      description: What browsers are you seeing the problem on ?
      multiple: true
      options:
        - Firefox
        - Chrome
        - Safari
        - Microsoft Edge
        - Opera
        - Brave
        - Other
    validations:
      required: false
  - type: dropdown
    id: os
    attributes:
      label: "OS"
      description: What is the impacted environment ?
      multiple: true
      options:
        - Windows
        - MacOS
        - Linux
        - Other
    validations:
      required: false
  - type: textarea
    id: additional_information
    attributes:
      label: "Additional information"
      description: Add any relevant information or context.
      placeholder:
    validations:
      required: false
