
{% set site_url = config.site_url | d(nav.homepage.url, true) | url %}
{% if not config.use_directory_urls and site_url[0] == site_url[-1] == "." %}
  {% set site_url = site_url ~ "/index.html" %}
{% endif %}
<header class="md-header" data-md-component="header">
  <nav
    class="md-header__inner md-grid"
    aria-label="{{ lang.t('header.title') }}"
  >
    <a
      href="{{ site_url }}"
      title="{{ config.site_name | e }}"
      class="md-header__button md-logo"
      aria-label="{{ config.site_name }}"
    >
      {% include "partials/logo.html" %}
    </a>
    <label class="md-header__button md-icon" for="__drawer">
      {% include ".icons/material/menu" ~ ".svg" %}
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            {{ config.site_name }}
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            {% if page and page.meta and page.meta.title %}
              {{ page.meta.title }}
            {% else %}
              {{ page.title }}
            {% endif %}
          </span>
        </div>
      </div>
    </div>
    <div class="md-header__options">
      <div class="md-header-nav__scheme md-header-nav__button md-source__icon md-icon">
          <a
            href="javascript:toggleScheme();"
            title="Light mode"
            class="light-mode"
          >
          {% set icon = "material/weather-sunny" %}
          {% include ".icons/" ~ icon ~ ".svg" %}
          </a>
          <a
            href="javascript:toggleScheme();"
            title="Dark mode"
            class="dark-mode"
          >
          {% set icon = "material/weather-night" %}
          {% include ".icons/" ~ icon ~ ".svg" %}
          </a>
          <a
            href="javascript:toggleScheme();"
            title="System preference"
            class="system-mode"
          >
          {% set icon = "material/theme-light-dark" %}
          {% include ".icons/" ~ icon ~ ".svg" %}
          </a>
          <!-- <a
            href="javascript:toggleScheme();"
            title="Unknown scheme"
            class="unknown-mode"
          >
          {% set icon = "material/help-circle" %}
          {% include ".icons/" ~ icon ~ ".svg" %}
          </a> -->
      </div>
    </div>
    {% if "material/search" in config.plugins %}
      <label class="md-header__button md-icon" for="__search">
        {% include ".icons/material/magnify.svg" %}
      </label>
      {% include "partials/search.html" %}
    {% endif %}
    {% if config.repo_url %}
      <div class="md-header__source">
        {% include "partials/source.html" %}
      </div>
    {% endif %}
  </nav>
</header>
