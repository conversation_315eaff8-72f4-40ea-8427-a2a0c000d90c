{"version": 3, "sources": ["palette/_colors.scss", "extra.css", "_general.scss", "extensions/_admonition.scss", "extensions/_arithmatex.scss", "extensions/_critic.scss", "extensions/_details.scss", "extensions/_highlight.scss", "extensions/_keys.scss", "extensions/_magiclink.scss", "extensions/_mark.scss", "extensions/_progressbar.scss", "extensions/_tabbed.scss", "extensions/_tables.scss", "extensions/_tasklist.scss", "extensions/_toc.scss", "extensions/_superfences.scss", "_material.scss", "utilities/_break.scss"], "names": [], "mappings": "iBAEA,QAEE,wBAAA,oBACA,+BAAA,6BACA,6BAAA,yBACA,yBAAA,wBACA,0BAAA,wBAEA,2BAAA,QACA,6BAAA,wBAEA,+BAAA,wBACA,6BAAA,wBAEA,0BAAA,gCACA,uBAAA,gCACA,2BAAA,iCACA,yBAAA,iCAGA,qBAAA,QAGA,qBAAA,oCACA,kBAAA,QACA,iBAAA,QACA,iBAAA,QACA,iBAAA,QACA,iBAAA,QACA,gBAAA,QAGA,uBAAA,QACA,8BAAA,QACA,8BAAA,uBAEA,mCAEE,wBAAA,uBACA,+BAAA,6BACA,2BAAA,QACA,6BAAA,qBAGA,uBAAA,qCACA,8BAAA,QACA,8BAAA,oCAIF,qCAEE,sBAAA,0BACA,6BAAA,0BACA,+BAAA,0BACA,gCAAA,0BACA,mCAAA,yBACA,eAAA,EAAA,OAAA,OAAA,oBAAA,CChBmC,EAAE,EAAE,QAAQ,qBDmB/C,sBAAA,oCACA,6BAAA,sBACA,+BAAA,sBACA,gCAAA,uBACA,6BAAA,oBAGA,4BAAA,QACA,8BAAA,QACA,+BAAA,QACA,kCAAA,QAGA,gBAAA,2BACA,mBAAA,2BAGA,yBAAA,2BAGA,mBAAA,kBACA,mBAAA,mBACA,yBAAA,uCACA,0BAAA,QACA,4BAAA,oBACA,+BAAA,kBACA,0BAAA,kBACA,2BAAA,mBACA,0BAAA,mBACA,2BAAA,oBACA,wBAAA,kBACA,4BAAA,mBACA,4BAAA,mBACA,2BAAA,mBACA,4BAAA,mBACA,2BAAA,mBACA,mBAAA,mBAGA,0BAAA,mBACA,uBAAA,oBACA,6BAAA,kBACA,2BAAA,mBACA,yBAAA,mBAEA,2BAAA,QACA,6BAAA,QACA,wBAAA,QAGA,qBAAA,mBACA,wBAAA,QACA,uBAAA,QACA,uBAAA,QAGA,qBAAA,qCACA,kBAAA,mBACA,iBAAA,mBACA,iBAAA,kBACA,iBAAA,mBACA,iBAAA,oBACA,gBAAA,kBAGA,uBAAA,qCACA,8BAAA,uCACA,8BAAA,oCC/BJ,uFD2DE,0DAIE,2BAAA,QACA,sBAAA,yBACA,mCAAA,2BACA,6BAAA,yBACA,4BAAA,yBACA,sBAAA,2BACA,6BAAA,kCC1DJ,wFDgDE,2DAIE,2BAAA,QACA,sBAAA,2BACA,mCAAA,6BACA,6BAAA,2BACA,4BAAA,2BACA,sBAAA,2BACA,6BAAA,kCC/CJ,0FDqCE,6DAIE,2BAAA,QACA,sBAAA,0BACA,mCAAA,4BACA,6BAAA,0BACA,4BAAA,0BACA,sBAAA,2BACA,6BAAA,kCCpCJ,+FD0BE,kEAIE,2BAAA,QACA,sBAAA,0BACA,mCAAA,4BACA,6BAAA,0BACA,4BAAA,0BACA,sBAAA,2BACA,6BAAA,kCCzBJ,wFDeE,2DAIE,2BAAA,QACA,sBAAA,0BACA,mCAAA,4BACA,6BAAA,0BACA,4BAAA,0BACA,sBAAA,2BACA,6BAAA,kCCdJ,0FDIE,6DAIE,2BAAA,QACA,sBAAA,0BACA,mCAAA,4BACA,6BAAA,0BACA,4BAAA,0BACA,sBAAA,2BACA,6BAAA,kCCHJ,8FDPE,iEAIE,2BAAA,QACA,sBAAA,0BACA,mCAAA,4BACA,6BAAA,0BACA,4BAAA,0BACA,sBAAA,2BACA,6BAAA,kCCQJ,wFDlBE,2DAIE,2BAAA,QACA,sBAAA,0BACA,mCAAA,4BACA,6BAAA,0BACA,4BAAA,0BACA,sBAAA,2BACA,6BAAA,kCCmBJ,wFD7BE,2DAIE,2BAAA,QACA,sBAAA,0BACA,mCAAA,4BACA,6BAAA,0BACA,4BAAA,0BACA,sBAAA,2BACA,6BAAA,kCC8BJ,yFDxCE,4DAIE,2BAAA,QACA,sBAAA,0BACA,mCAAA,4BACA,6BAAA,0BACA,4BAAA,0BACA,sBAAA,2BACA,6BAAA,kCCyCJ,+FDnDE,kEAIE,2BAAA,QACA,sBAAA,0BACA,mCAAA,4BACA,6BAAA,0BACA,4BAAA,0BACA,sBAAA,2BACA,6BAAA,kCCoDJ,wFD9DE,2DAIE,2BAAA,QACA,sBAAA,0BACA,mCAAA,4BACA,6BAAA,0BACA,4BAAA,0BACA,sBAAA,2BACA,6BAAA,kCC+DJ,0FDzEE,6DAIE,2BAAA,QACA,sBAAA,yBACA,mCAAA,2BACA,6BAAA,yBACA,4BAAA,yBACA,sBAAA,2BACA,6BAAA,kCC0EJ,yFDpFE,4DAIE,2BAAA,QACA,sBAAA,yBACA,mCAAA,2BACA,6BAAA,yBACA,4BAAA,yBACA,sBAAA,2BACA,6BAAA,kCCqFJ,0FD/FE,6DAIE,2BAAA,QACA,sBAAA,0BACA,mCAAA,4BACA,6BAAA,0BACA,4BAAA,0BACA,sBAAA,2BACA,6BAAA,kCCgGJ,+FD1GE,kEAIE,2BAAA,QACA,sBAAA,0BACA,mCAAA,4BACA,6BAAA,0BACA,4BAAA,0BACA,sBAAA,2BACA,6BAAA,kCC2GJ,uFD/EE,yDAEE,+BAAA,QACA,qBAAA,yBACA,kCAAA,2BACA,qBAAA,2BACA,4BAAA,kCCkFJ,wFDxFE,0DAEE,+BAAA,QACA,qBAAA,2BACA,kCAAA,6BACA,qBAAA,2BACA,4BAAA,kCC2FJ,0FDjGE,4DAEE,+BAAA,QACA,qBAAA,0BACA,kCAAA,4BACA,qBAAA,2BACA,4BAAA,kCCoGJ,+FD1GE,iEAEE,+BAAA,QACA,qBAAA,0BACA,kCAAA,4BACA,qBAAA,2BACA,4BAAA,kCC6GJ,wFDnHE,0DAEE,+BAAA,QACA,qBAAA,0BACA,kCAAA,4BACA,qBAAA,2BACA,4BAAA,kCCsHJ,0FD5HE,4DAEE,+BAAA,QACA,qBAAA,0BACA,kCAAA,4BACA,qBAAA,2BACA,4BAAA,kCC+HJ,8FDrIE,gEAEE,+BAAA,QACA,qBAAA,0BACA,kCAAA,4BACA,qBAAA,2BACA,4BAAA,kCCwIJ,wFD9IE,0DAEE,+BAAA,QACA,qBAAA,0BACA,kCAAA,4BACA,qBAAA,2BACA,4BAAA,kCCiJJ,wFDvJE,0DAEE,+BAAA,QACA,qBAAA,0BACA,kCAAA,4BACA,qBAAA,2BACA,4BAAA,kCC0JJ,yFDhKE,2DAEE,+BAAA,QACA,qBAAA,0BACA,kCAAA,4BACA,qBAAA,2BACA,4BAAA,kCCmKJ,+FDzKE,iEAEE,+BAAA,QACA,qBAAA,0BACA,kCAAA,4BACA,qBAAA,2BACA,4BAAA,kCC4KJ,wFDlLE,0DAEE,+BAAA,QACA,qBAAA,0BACA,kCAAA,4BACA,qBAAA,2BACA,4BAAA,kCCqLJ,0FD3LE,4DAEE,+BAAA,QACA,qBAAA,yBACA,kCAAA,2BACA,qBAAA,2BACA,4BAAA,kCC8LJ,yFDpME,2DAEE,+BAAA,QACA,qBAAA,yBACA,kCAAA,2BACA,qBAAA,2BACA,4BAAA,kCCuMJ,0FD7ME,4DAEE,+BAAA,QACA,qBAAA,0BACA,kCAAA,4BACA,qBAAA,2BACA,4BAAA,kCCgNJ,+FDtNE,iEAEE,+BAAA,QACA,qBAAA,0BACA,kCAAA,4BACA,qBAAA,2BACA,4BAAA,kCExMJ,MACE,WAAA,QACA,eAAA,QAEA,qBACE,cAAA,MAIF,qCACE,WAAA,oBACA,eAAA,kBAMA,eACE,OAAA,IAAA,EAAA,IAGF,0BACI,SAAA,SACA,IAAA,OACA,MAAA,MACA,MAAA,oCACA,WAAA,MAAA,MAEA,gCACE,MAAA,0BAGF,mCACI,OAAA,OAEA,uCACI,MAAA,OACA,OAAA,OAKZ,iDACE,WAAA,MAMF,yBAAA,+BACE,SAAA,SACA,MAAA,gBACA,UAAA,MAAA,KAAA,KAAA,SAKN,iBACE,GAAK,UAAA,SACL,IAAM,MAAA,oBAA4B,UAAA,WAClC,IAAM,UAAA,WACN,IAAM,MAAA,oBAA4B,UAAA,WAClC,KAAO,UAAA,UAGT,mBACE,WAAA,OAEA,sBACE,QAAA,aACA,MAAA,OACA,OAAA,EAAA,MACA,eAAA,OACA,cAAA,IAAA,MAAA,oCAIA,4BACI,aAAA,0BAKJ,8DACI,MAAA,8CAQJ,iGDqZJ,kGACA,mGCnZM,QAAA,KAMF,gGDiZJ,kGACA,mGC/YM,QAAA,KAKF,0ID8YJ,2IACA,4IC5YM,QAAA,KAKF,4DD2YJ,6DACA,+DCzYM,QAAA,KAKN,uBACE,QAAA,EAaF,kEACE,QAAA,KC7IF,wBAAA,oBACE,aAAA,EACA,kBAAA,IAwBF,QACE,yBAAA,YAWE,+BAAA,oyBACA,mCAAA,uBACA,qCAAA,QACA,uCAAA,uBAHA,0BAAA,oUACA,8BAAA,uBACA,gCAAA,QACA,kCAAA,uBAWA,+BAAA,uCACA,iCAAA,kBACA,mCAAA,yBAFA,mCAAA,uCACA,qCAAA,mBACA,uCAAA,yBAFA,+BAAA,uCACA,iCAAA,mBACA,mCAAA,yBAFA,8BAAA,uCACA,gCAAA,mBACA,kCAAA,yBAFA,kCAAA,uCACA,oCAAA,mBACA,sCAAA,wBAFA,mCAAA,uCACA,qCAAA,mBACA,uCAAA,yBAFA,kCAAA,uCACA,oCAAA,mBACA,sCAAA,yBAFA,kCAAA,uCACA,oCAAA,kBACA,sCAAA,uBAFA,iCAAA,uCACA,mCAAA,kBACA,qCAAA,uBAFA,8BAAA,uCACA,gCAAA,oBACA,kCAAA,wBAFA,kCAAA,uCACA,oCAAA,mBACA,sCAAA,yBAFA,gCAAA,uCACA,kCAAA,kBACA,oCAAA,yBAzBF,qCACE,2BAAA,kBAaA,qCACE,mCAAA,uCACA,qCAAA,oBACA,uCAAA,yBAHF,qCACE,8BAAA,uCACA,gCAAA,kBACA,kCAAA,yBAaN,uDAAA,mDACE,aAAA,sCAgBA,WAAA,oBAdA,oEAAA,gEACE,WAAA,EAAA,EAAA,EAAA,MAAA,wCAGF,yEAAA,qEAAA,2DACE,iBAAA,oCACA,iFAAA,6EAAA,mEACE,iBAAA,sCAEF,gFAAA,4EAAA,kEACE,MAAA,sCAeF,4DAAA,wDACE,aAAA,sCAEA,yEAAA,qEACE,WAAA,EAAA,EAAA,EAAA,MAAA,wCAIF,8EAAA,0EAAA,gEACE,iBAAA,oCACA,aAAA,sCAGA,sFAAA,kFAAA,wEACE,iBAAA,sCAEF,qFAAA,iFAAA,uEACE,MAAA,sCAjBN,gEAAA,4DACE,aAAA,0CAEA,6EAAA,yEACE,WAAA,EAAA,EAAA,EAAA,MAAA,4CAIF,kFAAA,8EAAA,oEACE,iBAAA,wCACA,aAAA,0CAGA,0FAAA,sFAAA,4EACE,iBAAA,0CAEF,yFAAA,qFAAA,2EACE,MAAA,0CAjBN,4DAAA,wDACE,aAAA,sCAEA,yEAAA,qEACE,WAAA,EAAA,EAAA,EAAA,MAAA,wCAIF,8EAAA,0EAAA,gEACE,iBAAA,oCACA,aAAA,sCAGA,sFAAA,kFAAA,wEACE,iBAAA,sCAEF,qFAAA,iFAAA,uEACE,MAAA,sCAjBN,2DAAA,uDACE,aAAA,qCAEA,wEAAA,oEACE,WAAA,EAAA,EAAA,EAAA,MAAA,uCAIF,6EAAA,yEAAA,+DACE,iBAAA,mCACA,aAAA,qCAGA,qFAAA,iFAAA,uEACE,iBAAA,qCAEF,oFAAA,gFAAA,sEACE,MAAA,qCAjBN,+DAAA,2DACE,aAAA,yCAEA,4EAAA,wEACE,WAAA,EAAA,EAAA,EAAA,MAAA,2CAIF,iFAAA,6EAAA,mEACE,iBAAA,uCACA,aAAA,yCAGA,yFAAA,qFAAA,2EACE,iBAAA,yCAEF,wFAAA,oFAAA,0EACE,MAAA,yCAjBN,gEAAA,4DACE,aAAA,0CAEA,6EAAA,yEACE,WAAA,EAAA,EAAA,EAAA,MAAA,4CAIF,kFAAA,8EAAA,oEACE,iBAAA,wCACA,aAAA,0CAGA,0FAAA,sFAAA,4EACE,iBAAA,0CAEF,yFAAA,qFAAA,2EACE,MAAA,0CAjBN,+DAAA,2DACE,aAAA,yCAEA,4EAAA,wEACE,WAAA,EAAA,EAAA,EAAA,MAAA,2CAIF,iFAAA,6EAAA,mEACE,iBAAA,uCACA,aAAA,yCAGA,yFAAA,qFAAA,2EACE,iBAAA,yCAEF,wFAAA,oFAAA,0EACE,MAAA,yCAjBN,+DAAA,2DACE,aAAA,yCAEA,4EAAA,wEACE,WAAA,EAAA,EAAA,EAAA,MAAA,2CAIF,iFAAA,6EAAA,mEACE,iBAAA,uCACA,aAAA,yCAGA,yFAAA,qFAAA,2EACE,iBAAA,yCAEF,wFAAA,oFAAA,0EACE,MAAA,yCAjBN,8DAAA,0DACE,aAAA,wCAEA,2EAAA,uEACE,WAAA,EAAA,EAAA,EAAA,MAAA,0CAIF,gFAAA,4EAAA,kEACE,iBAAA,sCACA,aAAA,wCAGA,wFAAA,oFAAA,0EACE,iBAAA,wCAEF,uFAAA,mFAAA,yEACE,MAAA,wCAjBN,2DAAA,uDACE,aAAA,qCAEA,wEAAA,oEACE,WAAA,EAAA,EAAA,EAAA,MAAA,uCAIF,6EAAA,yEAAA,+DACE,iBAAA,mCACA,aAAA,qCAGA,qFAAA,iFAAA,uEACE,iBAAA,qCAEF,oFAAA,gFAAA,sEACE,MAAA,qCAjBN,+DAAA,2DACE,aAAA,yCAEA,4EAAA,wEACE,WAAA,EAAA,EAAA,EAAA,MAAA,2CAIF,iFAAA,6EAAA,mEACE,iBAAA,uCACA,aAAA,yCAGA,yFAAA,qFAAA,2EACE,iBAAA,yCAEF,wFAAA,oFAAA,0EACE,MAAA,yCAjBN,6DAAA,yDACE,aAAA,uCAEA,0EAAA,sEACE,WAAA,EAAA,EAAA,EAAA,MAAA,yCAIF,+EAAA,2EAAA,iEACE,iBAAA,qCACA,aAAA,uCAGA,uFAAA,mFAAA,yEACE,iBAAA,uCAEF,sFAAA,kFAAA,wEACE,MAAA,uCAoBR,+BAAA,iCAAA,2BAAA,6BACE,aAAA,0CAEA,4CAAA,8CAAA,wCAAA,0CACE,WAAA,EAAA,EAAA,EAAA,MAAA,4CAGF,iDAAA,mDAAA,6CAAA,mCAAA,+CAAA,qCACE,iBAAA,wCACA,aAAA,0CAGA,yDAAA,2DAAA,qDAAA,2CAAA,uDAAA,6CACE,MAAA,KACA,OAAA,KACA,iBAAA,0CACA,gBAAA,KACA,mBAAA,oCAAA,WAAA,oCACA,QAAA,IAEF,wDAAA,0DAAA,oDAAA,0CAAA,sDAAA,4CACE,MAAA,0CArBN,4BAAA,wBACE,aAAA,qCAEA,yCAAA,qCACE,WAAA,EAAA,EAAA,EAAA,MAAA,uCAGF,8CAAA,0CAAA,gCACE,iBAAA,mCACA,aAAA,qCAGA,sDAAA,kDAAA,wCACE,MAAA,KACA,OAAA,KACA,iBAAA,qCACA,gBAAA,KACA,mBAAA,+BAAA,WAAA,+BACA,QAAA,IAEF,qDAAA,iDAAA,uCACE,MAAA,qCCjJR,4BACE,UAAA,eAGF,6BACE,UAAA,eH61BF,yDADA,6DADA,uDGr1BE,2DAEE,MAAA,KH01BJ,8CACA,8CGz1BE,4CHu1BF,4CGr1BI,WAAA,EAAA,KAAA,KAAA,eAKJ,wBACE,WAAA,eACA,WAAA,iBAKA,2BACE,QAAA,eACA,eAAA,IACA,UAAA,OACA,YAAA,SACA,gBAAA,cAGA,iCACE,QAAA,iBAGF,gCACE,SAAA,mBACA,QAAA,iBACA,YAAA,oBCvCF,uBJ83BJ,uBACA,wBI93BM,QAAA,EAAA,MACA,MAAA,MACA,WAAA,KAIJ,0BACE,OAAA,ECXF,oBAGE,SAAA,OAOE,kCACE,cAAA,KCZN,eACE,MAAA,iCAGF,eNo5BF,eMj5BI,MAAA,8BAGF,eACE,MAAA,+BNo5BJ,eMj5BE,eAEE,MAAA,gCAGF,eACE,MAAA,kCAGF,eN+4BF,eACA,eM74BI,MAAA,+BAEF,eN+4BF,eM74BI,MAAA,4BAOF,2BACI,OAAA,EACA,QAAA,EAAA,cACA,MAAA,wBACA,iBAAA,+BACA,cAAA,MACA,WAAA,KAKF,mBACE,MAAA,kBACA,iBAAA,uCACA,WAAA,MAAA,MACA,WAAA,iBAAA,MAGA,qBACE,MAAA,oCAGF,yBACE,iBAAA,8CAEA,2BACE,MAAA,oCAMR,qBACE,QAAA,EAIF,oBACE,WAAA,OAGF,uBAGE,cAAA,EAAA,YAAA,EAAA,SAAA,EAEA,+BACE,aAAA,MAOA,sDACE,iBAAA,gCAQF,gDACE,aAAA,eACA,YAAA,gBACA,cAAA,cACA,aAAA,eACA,iBAAA,gCAMN,qCAaE,SAAA,SACA,QAAA,MACA,WAAA,IACA,QAAA,KAAA,eAAA,KAAA,eACA,YAAA,IACA,UAAA,OACA,iBAAA,8BACA,uBAAA,MACA,wBAAA,MAlBA,yCACE,WAAA,EAEA,8CACE,uBAAA,EACA,wBAAA,EAgBJ,6CACE,SAAA,SACA,KAAA,cACA,MAAA,eACA,OAAA,eACA,iBAAA,2BACA,mBAAA,oSAAA,WAAA,oSACA,oBAAA,UAAA,YAAA,UACA,kBAAA,QAAA,UAAA,QACA,QAAA,GAIJ,2BACE,SAAA,SACA,WAAA,IACA,cAAA,IAEA,+BACE,WAAA,EACA,cAAA,EAGF,iCACE,QAAA,KAEA,8CACE,MAAA,KACA,OAAA,EACA,QAAA,MAAA,KAAA,MAAA,EAEA,oDACE,SAAA,SACA,OAAA,MACA,QAAA,MAAA,KACA,MAAA,2BACA,UAAA,IACA,iBAAA,2BACA,oBAAA,UAAA,YAAA,UACA,kBAAA,QAAA,UAAA,QACA,cAAA,MACA,OAAA,QACA,QAAA,GAEA,0DACE,iBAAA,0BAGF,4DACE,SAAA,SACA,IAAA,MACA,KAAA,MACA,QAAA,MACA,WAAA,WACA,MAAA,OACA,OAAA,OACA,iBAAA,2BACA,gBAAA,OACA,QAAA,GAGF,2DACE,QAAA,KAEA,mEACE,mBAAA,+NAAA,WAAA,+NAGJ,qEACE,mBAAA,8NAAA,WAAA,8NAMJ,mEACE,QAAA,OAGF,qEACE,QAAA,KAGF,4DACE,WAAA,QACA,SAAA,OAGF,sDACE,SAAA,SACA,OAAA,EACA,KAAA,EACA,QAAA,IAAA,KAAA,KAAA,MACA,iBAAA,2EC1NJ,qCACE,cAAA,KACA,QAAA,IAFF,2CACE,cAAA,KACA,QAAA,ICZR,QACE,uBAAA,uOACA,wBAAA,gqBACA,2BAAA,sTACA,wBAAA,gqBACA,wBAAA,mSACA,yBAAA,6hBACA,sBAAA,iwBACA,uBAAA,yUACA,4BAAA,uyBAIA,8DACE,mBAAA,4BAAA,WAAA,4BRmmCJ,qDADA,sDAIA,yDADA,oDADA,mDQ1lCI,yDR6lCJ,sDQ5lCM,SAAA,SACA,aAAA,QRimCN,6DADA,8DAIA,iEADA,4DADA,2DQhmCM,iERmmCN,8DQlmCQ,SAAA,SACA,IAAA,EACA,KAAA,EACA,QAAA,MACA,WAAA,WACA,MAAA,OACA,OAAA,OACA,iBAAA,0BACA,gBAAA,OACA,WAAA,iBAAA,MACA,oBAAA,UAAA,YAAA,UACA,kBAAA,QAAA,UAAA,QACA,QAAA,GRymCR,mEADA,oEAIA,uEADA,kEADA,iEQvmCM,uER0mCN,oEQzmCQ,iBAAA,0BAgBF,6DACE,mBAAA,6BAAA,WAAA,6BADF,8DACE,mBAAA,8BAAA,WAAA,8BADF,2DACE,mBAAA,2BAAA,WAAA,2BADF,4DACE,mBAAA,4BAAA,WAAA,4BADF,iEACE,mBAAA,iCAAA,WAAA,iCAcF,kFACE,mBAAA,6BAAA,WAAA,6BADF,kFACE,mBAAA,6BAAA,WAAA,6BADF,qFACE,mBAAA,gCAAA,WAAA,gCC7ER,8BACE,WAAA,KCCA,4BACE,SAAA,SACA,MAAA,KACA,OAAA,EACA,MAAA,qBACA,YAAA,IACA,YAAA,OACA,YAAA,OACA,WAAA,OACA,YAAA,SAAA,SAAA,OAAA,iCAAA,CAAA,QAAA,SAAA,OAAA,iCAAA,CAAA,SAAA,QAAA,OAAA,iCAAA,CAAA,QAAA,QAAA,OAAA,kCAOF,0BACE,MAAA,KACA,OAAA,OACA,iBAAA,QAKA,+CACE,UAAA,gBAAA,GAAA,OAAA,SAIJ,sBACE,SAAA,SACA,QAAA,MACA,MAAA,KACA,OAAA,OACA,OAAA,MAAA,EACA,iBAAA,qCAEA,2BACE,OAAA,MACA,WAAA,MAEA,2CACE,WAAA,OAGF,yCACE,OAAA,MAIJ,gDACE,iBAAA,8KAWA,gBAAA,KAAA,KAaA,4CACE,iBAAA,uBADF,2CACE,iBAAA,sBADF,2CACE,iBAAA,sBADF,2CACE,iBAAA,sBADF,2CACE,iBAAA,sBADF,0CACE,iBAAA,qBAKN,2BACE,GACE,oBAAA,EAAA,EAGF,KACE,oBAAA,KAAA,GCvFF,sEACE,WAAA,EAAA,QAAA,oCAAA,MASF,yDACE,MAAA,KAEA,iEACE,MAAA,OACA,QAAA,EAIJ,uEACE,QAAA,EAAA,MX2wCN,8FACA,mGACA,6FACA,kGWrwCU,sFAME,aAAA,QACA,YAAA,QACA,cAAA,MACA,aAAA,MXmwCZ,4GACA,iHACA,2GACA,gHW7wCY,oGACE,WAAA,EAUN,iGACE,WAAA,EAEA,aAAA,QACA,YAAA,QACA,cAAA,MACA,aAAA,MAEA,8GACE,KAAA,MAIJ,8FACE,aAAA,QACA,YAAA,QACA,cAAA,MACA,aAAA,MAOV,uCACE,iBAAA,0BCpEA,8DACE,WAAA,oBAEA,uEACE,iBAAA,gBAGF,iEACE,MAAA,qBACA,iBAAA,uCACA,cAAA,OAAA,MAAA,2BAGF,iEACE,WAAA,OAAA,MAAA,oCCbF,2FACC,iBAAA,oCAGD,mHACC,iBAAA,QCNH,wBACE,MAAA,IACA,OAAA,IACA,eAAA,OACA,iBAAA,oCACA,gBAAA,IACA,kBAAA,IAAA,UAAA,IACA,oBAAA,UAAA,YAAA,UACA,WAAA,QACA,mBAAA,0UAAA,WAAA,0UAGF,8Bdi2CF,oCc/1CI,iBAAA,0BCfJ,YACE,SAAA,KCFF,KACE,iBAAA,YAGF,sCACE,MAAA,2BAIF,+BAEE,gBAAA,2BACA,iBAAA,2BAGA,qBAAA,YACA,2BAAA,oCAGA,qBAAA,qBACA,qBAAA,oCAGA,0CACE,MAAA,qBACA,iBAAA,0BACA,cAAA,OAAA,MAAA,2BAEA,gEACI,WAAA,EAAA,EAAA,MAAA,eAAA,CAAA,EAAA,EAAA,MAAA,MAAA,eAKN,uCACE,iBAAA,iCAEA,6CACE,iBAAA,2BAKJ,wCACE,MAAA,qBACA,iBAAA,wCAEA,sDACI,MAAA,2BAGJ,oDACI,MAAA,0BAKN,wCACE,MAAA,qBACA,iBAAA,wCAIF,+CACE,MAAA,qBAGF,iEACE,MAAA,kCAGF,mEACE,MAAA,2BAIA,mDAAA,mDAEE,MAAA,0BAkCJ,iDACE,MAAA,qBACA,iBAAA,iCAEA,uDACE,iBAAA,kCAGF,kEACE,MAAA,qBAEF,mEACE,MAAA,kCADF,8DACE,MAAA,kCAIJ,4FACE,iBAAA,YAGF,mDACE,MAAA,wCAGF,2CAAA,mDACE,iBAAA,kCAKA,yDACE,MAAA,2BAGF,+CACE,WAAA,OAAA,MAAA,2BAKJ,4DACE,iBAAA,uCAMF,eACE,MAAA,qBACA,eAAA,KhB2zCJ,uBADA,wBAMA,wBAEA,2BAJA,wBADA,wBAEA,wBAEA,2BALA,qBgBxzCA,wBA+BE,gBAAA,oCAAA,YACA,gBAAA,KhBoyCF,0CADA,2CAMA,2CAEA,8CAJA,2CADA,2CAEA,2CAEA,8CALA,wCgB1zCE,2CACE,MAAA,MACA,OAAA,MhBo0CJ,iDADA,kDAMA,kDAEA,qDAJA,kDADA,kDAEA,kDAEA,qDALA,+CgBl0CE,kDACE,iBAAA,YhB40CJ,gDADA,iDAMA,iDAEA,oDAJA,iDADA,iDAEA,iDAEA,oDALA,8CgBz0CE,iDACE,iBAAA,oChBm1CJ,sDADA,uDAMA,uDAEA,0DAJA,uDADA,uDAEA,uDAEA,0DALA,oDgBj1CI,uDACE,iBAAA,0BhB21CN,6BADA,8BAMA,8BAEA,iCAJA,8BADA,8BAEA,8BAEA,iCALA,2BgBn1CE,8BACE,gBAAA,0BAAA,YCIA,wChB1EF,uBACE,cAAA,EAGF,oBACE,aAAA,EerCA,+CACE,MAAA,qBACA,iBAAA,wCAGF,sDACE,MAAA,qBACA,iBAAA,0BACA,cAAA,OAAA,MAAA,4BCkGF,wCXoCE,wBACE,aAAA,OACA,YAAA,OAEF,2BACE,aAAA,OACA,YAAA,OAEA,0CACE,KAAA,MK/KR,wDACE,aAAA,EAGF,8CACE,UAAA,KACA,OAAA,EACA,qBAAA,EACA,4BAAA,EAEA,qDACE,mBAAA,EACA,QAAA,KAIF,oEACE,oBAAA,EACA,qBAAA,EAIF,oEACE,kBAAA,EACA,mBAAA,GM0GF,wCDzHA,gGACE,MAAA,2BAIF,+DACE,MAAA,qBACA,iBAAA,0BACA,cAAA,OAAA,MAAA", "file": "extra-fb5a2a1c86.css", "sourcesContent": ["@import \"./dracula\";\n\n:root > * {\n  // Custom code colors\n  --md-code-link-bg-color: hsla(0, 0%, 96%, 1);\n  --md-code-link-accent-bg-color: var(--md-code-link-bg-color);\n  --md-default-bg-color--trans: rgb(100%, 100%, 100%, 0);\n  --md-code-title-bg-color: var(--md-code-bg-color);\n  --md-code-inline-bg-color: var(--md-code-bg-color);\n\n  --md-code-special-bg-color: #{darken(hsl(0, 0%, 96%), 5%)};\n  --md-code-alternate-bg-color: var(--md-code-bg-color);\n\n  --md-code-hl-punctuation-color: var(--md-code-fg-color);\n  --md-code-hl-namespace-color: var(--md-code-fg-color);\n\n  --md-code-hl-entity-color: var(--md-code-hl-keyword-color);\n  --md-code-hl-tag-color: var(--md-code-hl-keyword-color);\n  --md-code-hl-builtin-color: var(--md-code-hl-constant-color);\n  --md-code-hl-class-color: var(--md-code-hl-function-color);\n\n  // Various Material related color variables\n  --md-typeset-a-color: #{$clr-cyan-500};\n\n  // Progressbar colors\n  --md-progress-stripe: var(--md-default-bg-color--lighter);\n  --md-progress-100: #{$clr-green-a400};\n  --md-progress-80: #{$clr-green-a400};\n  --md-progress-60: #{$clr-yellow-700};\n  --md-progress-40: #{$clr-orange-a400};\n  --md-progress-20: #{$clr-red-a200};\n  --md-progress-0: #{$clr-red-a400};\n\n  // Keys colors\n  --md-typeset-kbd-color: #{shade(hsla(0, 100%, 100%, 1), 8%)};\n  --md-typeset-kbd-border-color: #{shade(hsla(0, 100%, 100%, 1), 28%)};\n  --md-typeset-kbd-accent-color: hsla(0, 100%, 100%, 1);\n\n  &[data-md-color-scheme=\"slate\"] {\n    // Custom code colors\n    --md-code-link-bg-color: hsla(232, 15%, 15%, 1);\n    --md-code-link-accent-bg-color: var(--md-code-link-bg-color);\n    --md-code-special-bg-color: #{lighten(hsl(232, 15%, 15%), 5%)};\n    --md-default-bg-color--trans: hsla(232,15%,15%, 0);\n\n    // Keys colors\n    --md-typeset-kbd-color: var(--md-default-fg-color--lightest);\n    --md-typeset-kbd-border-color: #{darken(rgb(46, 48, 62), 9%)};\n    --md-typeset-kbd-accent-color: var(--md-default-fg-color--lighter);\n  }\n\n  // Dark mode color changes\n  &[data-md-color-scheme=\"dracula\"] {\n    // Default color shades\n    --md-default-fg-color:               #{transparentize($drac-fg, 0.13)};\n    --md-default-fg-color--light:        #{transparentize($drac-fg, 0.46)};\n    --md-default-fg-color--lighter:      #{transparentize($drac-fg, 0.84)};\n    --md-default-fg-color--lightest:     #{transparentize($drac-fg, 0.93)};\n    --md-default-autocomplete-fg-color:  #{transparentize($drac-fg, 0.60)};\n    --md-shadow-z2:                      0 #{px2rem(4px)} #{px2rem(10px)} hsla(0, 0%, 0%, 0.3),\n                                         0 0              #{px2rem(1px)}  hsla(0, 0%, 0%, 0.2);\n\n    --md-default-bg-color:               var(--md-default-bg-color--darkest);\n    --md-default-bg-color--light:        #{transparentize($drac-default-bg, 0.3)};\n    --md-default-bg-color--lighter:      #{transparentize($drac-default-bg, 0.7)};\n    --md-default-bg-color--lightest:     #{transparentize($drac-default-bg, 0.88)};\n    --md-default-bg-color--trans:        #{transparentize($drac-default-bg, 1)};\n\n    // Dark specific colors\n    --md-default-bg-color--dark:         #{darken($drac-default-bg, 3%)};\n    --md-default-bg-color--darker:       #{darken($drac-default-bg, 6%)};\n    --md-default-bg-color--darkest:      #{darken($drac-default-bg, 9%)};\n    --md-default-bg-color--ultra-dark:   #{darken($drac-default-bg, 15%)};\n\n    // General text\n    --md-text-color: var(--md-default-fg-color);\n    --md-typeset-color: var(--md-default-fg-color);\n\n    // Admonition colors\n    --md-admonition-fg-color: var(--md-default-fg-color);\n\n    // Code colors\n    --md-code-fg-color: #{$drac-fg};\n    --md-code-bg-color: #{$drac-bg};\n    --md-code-title-bg-color: var(--md-default-bg-color--ultra-dark);\n    --md-code-inline-bg-color: #{lighten($drac-bg, 5%)};\n    --md-code-hl-operator-color: #{$drac-pink};\n    --md-code-hl-punctuation-color: #{$drac-fg};\n    --md-code-hl-string-color: #{$drac-yellow};\n    --md-code-hl-special-color: #{$drac-purple};\n    --md-code-hl-number-color: #{$drac-purple};\n    --md-code-hl-keyword-color: #{$drac-pink};\n    --md-code-hl-name-color: #{$drac-fg};\n    --md-code-hl-constant-color: #{$drac-purple};\n    --md-code-hl-function-color: #{$drac-green};\n    --md-code-hl-comment-color: #{$drac-blue};\n    --md-code-hl-variable-color: #{$drac-orange};\n    --md-code-hl-generic-color: #{$drac-blue};\n    --md-code-hl-color: #{$drac-selection};\n\n    // Custom code colors\n    --md-code-hl-entity-color: #{$drac-green};\n    --md-code-hl-tag-color: #{$drac-pink};\n    --md-code-hl-namespace-color: #{$drac-fg};\n    --md-code-hl-builtin-color: #{$drac-cyan};\n    --md-code-hl-class-color: #{$drac-cyan};\n\n    --md-code-special-bg-color: #{darken($drac-bg, 5%)};\n    --md-code-alternate-bg-color: #{tint($drac-bg, 10%)};\n    --md-code-link-bg-color: #{mix($drac-cyan, $drac-bg, 15%)};\n\n    // Various Material related color variables\n    --md-typeset-a-color: #{$drac-cyan};\n    --md-typeset-mark-color: #{mix($drac-yellow, $drac-bg, 35%)};\n    --md-typeset-del-color: #{mix($drac-pink, $drac-bg, 35%)};\n    --md-typeset-ins-color: #{mix($drac-green, $drac-bg, 35%)};\n\n    // Progressbar colors\n    --md-progress-stripe: var(--md-default-bg-color--lightest);\n    --md-progress-100: #{$drac-green};\n    --md-progress-80: #{$drac-light-green};\n    --md-progress-60: #{$drac-yellow};\n    --md-progress-40: #{$drac-orange};\n    --md-progress-20: #{$drac-pink};\n    --md-progress-0: #{$drac-red};\n\n    // Keys colors\n    --md-typeset-kbd-color: var(--md-default-fg-color--lightest);\n    --md-typeset-kbd-border-color: var(--md-default-bg-color--ultra-dark);\n    --md-typeset-kbd-accent-color: var(--md-default-fg-color--lighter);\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: primary colors\n// ----------------------------------------------------------------------------\n\n@each $name, $colors in (\n  \"red\":         $drac-red         lighten($drac-red, 5%)   darken($drac-red, 5%),\n  \"pink\":        $drac-pink        lighten($drac-pink, 5%)   darken($drac-pink, 5%),\n  \"purple\":      $drac-purple      lighten($drac-purple, 5%)   darken($drac-purple, 5%),\n  \"deep-purple\": $drac-purple      lighten($drac-purple, 5%)   darken($drac-purple, 5%),\n  \"blue\":        $drac-blue        lighten($drac-blue, 5%)   darken($drac-blue, 5%),\n  \"indigo\":      $drac-blue        lighten($drac-blue, 5%)   darken($drac-blue, 5%),\n  \"light-blue\":  $drac-blue        lighten($drac-blue, 5%)   darken($drac-blue, 5%),\n  \"cyan\":        $drac-cyan        lighten($drac-cyan, 5%)   darken($drac-cyan, 5%),\n  \"teal\":        $drac-cyan        lighten($drac-cyan, 5%)   darken($drac-cyan, 5%),\n  \"green\":       $drac-green       lighten($drac-green, 5%)   darken($drac-green, 5%),\n  \"light-green\": $drac-green       lighten($drac-green, 5%)   darken($drac-green, 5%),\n  \"lime\":        $drac-green       lighten($drac-green, 5%)   darken($drac-green, 5%),\n  \"yellow\":      $drac-yellow      lighten($drac-yellow, 5%)   darken($drac-yellow, 5%),\n  \"amber\":       $drac-yellow      lighten($drac-yellow, 5%)   darken($drac-yellow, 5%),\n  \"orange\":      $drac-orange      lighten($drac-orange, 5%)   darken($drac-orange, 5%),\n  \"deep-orange\": $drac-orange      lighten($drac-orange, 5%)   darken($drac-orange, 5%)\n) {\n\n  // Color palette\n  [data-md-color-scheme=\"dracula\"][data-md-color-primary=\"#{$name}\"],\n  [data-md-color-scheme=\"dracula\"] :not([data-md-color-scheme])[data-md-color-primary=\"#{$name}\"]\n   {\n\n    --md-primary-code-bg-color:         #{mix($drac-bg, nth($colors, 1), 85%)};\n    --md-primary-fg-color:              hsla(#{hex2hsl(nth($colors, 1))}, 1);\n    --md-primary-fg-color--transparent: hsla(#{hex2hsl(nth($colors, 1))}, 0.1);\n    --md-primary-fg-color--light:       hsla(#{hex2hsl(nth($colors, 2))}, 1);\n    --md-primary-fg-color--dark:        hsla(#{hex2hsl(nth($colors, 3))}, 1);\n    --md-primary-bg-color:              var(--md-default-bg-color);\n    --md-primary-bg-color--light:       var(--md-default-bg-color--light);\n  }\n}\n\n// ----------------------------------------------------------------------------\n// Rules: accent colors\n// ----------------------------------------------------------------------------\n\n@each $name, $color in (\n  \"red\":         darken($drac-red, 5%),\n  \"pink\":        darken($drac-pink, 5%),\n  \"purple\":      darken($drac-purple, 5%),\n  \"deep-purple\": darken($drac-purple, 5%),\n  \"blue\":        darken($drac-blue, 5%),\n  \"indigo\":      darken($drac-blue, 5%),\n  \"light-blue\":  darken($drac-blue, 5%),\n  \"cyan\":        darken($drac-cyan, 5%),\n  \"teal\":        darken($drac-cyan, 5%),\n  \"green\":       darken($drac-green, 5%),\n  \"light-green\": darken($drac-green, 5%),\n  \"lime\":        darken($drac-green, 5%),\n  \"yellow\":      darken($drac-yellow, 5%),\n  \"amber\":       darken($drac-yellow, 5%),\n  \"orange\":      darken($drac-orange, 5%),\n  \"deep-orange\": darken($drac-orange, 5%)\n) {\n\n  // Color palette\n  [data-md-color-scheme=\"dracula\"][data-md-color-accent=\"#{$name}\"],\n  [data-md-color-scheme=\"dracula\"] :not([data-md-color-scheme])[data-md-color-primary=\"#{$name}\"] {\n    --md-code-link-accent-bg-color:    #{mix($color, $drac-bg, 15%)};\n    --md-accent-fg-color:              hsla(#{hex2hsl($color)}, 1);\n    --md-accent-fg-color--transparent: hsla(#{hex2hsl($color)}, 0.1);\n    --md-accent-bg-color:              var(--md-default-bg-color);\n    --md-accent-bg-color--light:       var(--md-default-bg-color--light);\n  }\n}\n", "@charset \"UTF-8\";\n:root > * {\n  --md-code-link-bg-color: hsla(0, 0%, 96%, 1);\n  --md-code-link-accent-bg-color: var(--md-code-link-bg-color);\n  --md-default-bg-color--trans: rgb(100%, 100%, 100%, 0);\n  --md-code-title-bg-color: var(--md-code-bg-color);\n  --md-code-inline-bg-color: var(--md-code-bg-color);\n  --md-code-special-bg-color: #e8e8e8;\n  --md-code-alternate-bg-color: var(--md-code-bg-color);\n  --md-code-hl-punctuation-color: var(--md-code-fg-color);\n  --md-code-hl-namespace-color: var(--md-code-fg-color);\n  --md-code-hl-entity-color: var(--md-code-hl-keyword-color);\n  --md-code-hl-tag-color: var(--md-code-hl-keyword-color);\n  --md-code-hl-builtin-color: var(--md-code-hl-constant-color);\n  --md-code-hl-class-color: var(--md-code-hl-function-color);\n  --md-typeset-a-color: #00bcd4;\n  --md-progress-stripe: var(--md-default-bg-color--lighter);\n  --md-progress-100: #00e676;\n  --md-progress-80: #00e676;\n  --md-progress-60: #fbc02d;\n  --md-progress-40: #ff9100;\n  --md-progress-20: #ff5252;\n  --md-progress-0: #ff1744;\n  --md-typeset-kbd-color: #ebebeb;\n  --md-typeset-kbd-border-color: #b8b8b8;\n  --md-typeset-kbd-accent-color: hsla(0, 100%, 100%, 1);\n}\n:root > *[data-md-color-scheme=slate] {\n  --md-code-link-bg-color: hsla(232, 15%, 15%, 1);\n  --md-code-link-accent-bg-color: var(--md-code-link-bg-color);\n  --md-code-special-bg-color: #2b2d3b;\n  --md-default-bg-color--trans: hsla(232,15%,15%, 0);\n  --md-typeset-kbd-color: var(--md-default-fg-color--lightest);\n  --md-typeset-kbd-border-color: #1a1c24;\n  --md-typeset-kbd-accent-color: var(--md-default-fg-color--lighter);\n}\n:root > *[data-md-color-scheme=dracula] {\n  --md-default-fg-color: rgba(248, 248, 242, 0.87);\n  --md-default-fg-color--light: rgba(248, 248, 242, 0.54);\n  --md-default-fg-color--lighter: rgba(248, 248, 242, 0.16);\n  --md-default-fg-color--lightest: rgba(248, 248, 242, 0.07);\n  --md-default-autocomplete-fg-color: rgba(248, 248, 242, 0.4);\n  --md-shadow-z2: 0 0.2rem 0.5rem hsla(0, 0%, 0%, 0.3),\n                                       0 0 0.05rem hsla(0, 0%, 0%, 0.2);\n  --md-default-bg-color: var(--md-default-bg-color--darkest);\n  --md-default-bg-color--light: rgba(50, 52, 67, 0.7);\n  --md-default-bg-color--lighter: rgba(50, 52, 67, 0.3);\n  --md-default-bg-color--lightest: rgba(50, 52, 67, 0.12);\n  --md-default-bg-color--trans: rgba(50, 52, 67, 0);\n  --md-default-bg-color--dark: #2b2e3b;\n  --md-default-bg-color--darker: #252732;\n  --md-default-bg-color--darkest: #1e2029;\n  --md-default-bg-color--ultra-dark: #111217;\n  --md-text-color: var(--md-default-fg-color);\n  --md-typeset-color: var(--md-default-fg-color);\n  --md-admonition-fg-color: var(--md-default-fg-color);\n  --md-code-fg-color: hsl(60, 30%, 96%);\n  --md-code-bg-color: hsl(231, 15%, 18%);\n  --md-code-title-bg-color: var(--md-default-bg-color--ultra-dark);\n  --md-code-inline-bg-color: #323443;\n  --md-code-hl-operator-color: hsl(326, 100%, 74%);\n  --md-code-hl-punctuation-color: hsl(60, 30%, 96%);\n  --md-code-hl-string-color: hsl(65, 92%, 76%);\n  --md-code-hl-special-color: hsl(265, 89%, 78%);\n  --md-code-hl-number-color: hsl(265, 89%, 78%);\n  --md-code-hl-keyword-color: hsl(326, 100%, 74%);\n  --md-code-hl-name-color: hsl(60, 30%, 96%);\n  --md-code-hl-constant-color: hsl(265, 89%, 78%);\n  --md-code-hl-function-color: hsl(135, 94%, 65%);\n  --md-code-hl-comment-color: hsl(225, 27%, 51%);\n  --md-code-hl-variable-color: hsl(31, 100%, 71%);\n  --md-code-hl-generic-color: hsl(225, 27%, 51%);\n  --md-code-hl-color: hsl(231, 25%, 25%);\n  --md-code-hl-entity-color: hsl(135, 94%, 65%);\n  --md-code-hl-tag-color: hsl(326, 100%, 74%);\n  --md-code-hl-namespace-color: hsl(60, 30%, 96%);\n  --md-code-hl-builtin-color: hsl(191, 97%, 77%);\n  --md-code-hl-class-color: hsl(191, 97%, 77%);\n  --md-code-special-bg-color: #1c1e26;\n  --md-code-alternate-bg-color: #3d3e49;\n  --md-code-link-bg-color: #364653;\n  --md-typeset-a-color: hsl(191, 97%, 77%);\n  --md-typeset-mark-color: #6e7252;\n  --md-typeset-del-color: #734568;\n  --md-typeset-ins-color: #36724e;\n  --md-progress-stripe: var(--md-default-bg-color--lightest);\n  --md-progress-100: hsl(135, 94%, 65%);\n  --md-progress-80: hsl(135, 92%, 79%);\n  --md-progress-60: hsl(65, 92%, 76%);\n  --md-progress-40: hsl(31, 100%, 71%);\n  --md-progress-20: hsl(326, 100%, 74%);\n  --md-progress-0: hsl(0, 100%, 67%);\n  --md-typeset-kbd-color: var(--md-default-fg-color--lightest);\n  --md-typeset-kbd-border-color: var(--md-default-bg-color--ultra-dark);\n  --md-typeset-kbd-accent-color: var(--md-default-fg-color--lighter);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=red],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=red] {\n  --md-primary-code-bg-color: #47303a;\n  --md-primary-fg-color: hsla(0deg, 100%, 67%, 1);\n  --md-primary-fg-color--transparent: hsla(0deg, 100%, 67%, 0.1);\n  --md-primary-fg-color--light: hsla(0deg, 100%, 72%, 1);\n  --md-primary-fg-color--dark: hsla(0deg, 100%, 62%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=pink],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=pink] {\n  --md-primary-code-bg-color: #47354b;\n  --md-primary-fg-color: hsla(326deg, 100%, 74%, 1);\n  --md-primary-fg-color--transparent: hsla(326deg, 100%, 74%, 0.1);\n  --md-primary-fg-color--light: hsla(326deg, 100%, 79%, 1);\n  --md-primary-fg-color--dark: hsla(326deg, 100%, 69%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=purple],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=purple] {\n  --md-primary-code-bg-color: #3e3952;\n  --md-primary-fg-color: hsla(265deg, 89%, 78%, 1);\n  --md-primary-fg-color--transparent: hsla(265deg, 89%, 78%, 0.1);\n  --md-primary-fg-color--light: hsla(265deg, 89%, 83%, 1);\n  --md-primary-fg-color--dark: hsla(265deg, 89%, 73%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=deep-purple],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=deep-purple] {\n  --md-primary-code-bg-color: #3e3952;\n  --md-primary-fg-color: hsla(265deg, 89%, 78%, 1);\n  --md-primary-fg-color--transparent: hsla(265deg, 89%, 78%, 0.1);\n  --md-primary-fg-color--light: hsla(265deg, 89%, 83%, 1);\n  --md-primary-fg-color--dark: hsla(265deg, 89%, 73%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=blue],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=blue] {\n  --md-primary-code-bg-color: #303446;\n  --md-primary-fg-color: hsla(225deg, 27%, 51%, 1);\n  --md-primary-fg-color--transparent: hsla(225deg, 27%, 51%, 0.1);\n  --md-primary-fg-color--light: hsla(225deg, 27%, 56%, 1);\n  --md-primary-fg-color--dark: hsla(225deg, 27%, 46%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=indigo],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=indigo] {\n  --md-primary-code-bg-color: #303446;\n  --md-primary-fg-color: hsla(225deg, 27%, 51%, 1);\n  --md-primary-fg-color--transparent: hsla(225deg, 27%, 51%, 0.1);\n  --md-primary-fg-color--light: hsla(225deg, 27%, 56%, 1);\n  --md-primary-fg-color--dark: hsla(225deg, 27%, 46%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=light-blue],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=light-blue] {\n  --md-primary-code-bg-color: #303446;\n  --md-primary-fg-color: hsla(225deg, 27%, 51%, 1);\n  --md-primary-fg-color--transparent: hsla(225deg, 27%, 51%, 0.1);\n  --md-primary-fg-color--light: hsla(225deg, 27%, 56%, 1);\n  --md-primary-fg-color--dark: hsla(225deg, 27%, 46%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=cyan],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=cyan] {\n  --md-primary-code-bg-color: #364653;\n  --md-primary-fg-color: hsla(191deg, 97%, 77%, 1);\n  --md-primary-fg-color--transparent: hsla(191deg, 97%, 77%, 0.1);\n  --md-primary-fg-color--light: hsla(191deg, 97%, 82%, 1);\n  --md-primary-fg-color--dark: hsla(191deg, 97%, 72%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=teal],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=teal] {\n  --md-primary-code-bg-color: #364653;\n  --md-primary-fg-color: hsla(191deg, 97%, 77%, 1);\n  --md-primary-fg-color--transparent: hsla(191deg, 97%, 77%, 0.1);\n  --md-primary-fg-color--light: hsla(191deg, 97%, 82%, 1);\n  --md-primary-fg-color--dark: hsla(191deg, 97%, 72%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=green],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=green] {\n  --md-primary-code-bg-color: #2d4840;\n  --md-primary-fg-color: hsla(135deg, 94%, 65%, 1);\n  --md-primary-fg-color--transparent: hsla(135deg, 94%, 65%, 0.1);\n  --md-primary-fg-color--light: hsla(135deg, 94%, 70%, 1);\n  --md-primary-fg-color--dark: hsla(135deg, 94%, 60%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=light-green],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=light-green] {\n  --md-primary-code-bg-color: #2d4840;\n  --md-primary-fg-color: hsla(135deg, 94%, 65%, 1);\n  --md-primary-fg-color--transparent: hsla(135deg, 94%, 65%, 0.1);\n  --md-primary-fg-color--light: hsla(135deg, 94%, 70%, 1);\n  --md-primary-fg-color--dark: hsla(135deg, 94%, 60%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=lime],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=lime] {\n  --md-primary-code-bg-color: #2d4840;\n  --md-primary-fg-color: hsla(135deg, 94%, 65%, 1);\n  --md-primary-fg-color--transparent: hsla(135deg, 94%, 65%, 0.1);\n  --md-primary-fg-color--light: hsla(135deg, 94%, 70%, 1);\n  --md-primary-fg-color--dark: hsla(135deg, 94%, 60%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=yellow],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=yellow] {\n  --md-primary-code-bg-color: #454842;\n  --md-primary-fg-color: hsla(65deg, 92%, 76%, 1);\n  --md-primary-fg-color--transparent: hsla(65deg, 92%, 76%, 0.1);\n  --md-primary-fg-color--light: hsla(65deg, 92%, 81%, 1);\n  --md-primary-fg-color--dark: hsla(65deg, 92%, 71%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=amber],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=amber] {\n  --md-primary-code-bg-color: #454842;\n  --md-primary-fg-color: hsla(65deg, 92%, 76%, 1);\n  --md-primary-fg-color--transparent: hsla(65deg, 92%, 76%, 0.1);\n  --md-primary-fg-color--light: hsla(65deg, 92%, 81%, 1);\n  --md-primary-fg-color--dark: hsla(65deg, 92%, 71%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=orange],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=orange] {\n  --md-primary-code-bg-color: #473e3d;\n  --md-primary-fg-color: hsla(31deg, 100%, 71%, 1);\n  --md-primary-fg-color--transparent: hsla(31deg, 100%, 71%, 0.1);\n  --md-primary-fg-color--light: hsla(31deg, 100%, 76%, 1);\n  --md-primary-fg-color--dark: hsla(31deg, 100%, 66%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-primary=deep-orange],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=deep-orange] {\n  --md-primary-code-bg-color: #473e3d;\n  --md-primary-fg-color: hsla(31deg, 100%, 71%, 1);\n  --md-primary-fg-color--transparent: hsla(31deg, 100%, 71%, 0.1);\n  --md-primary-fg-color--light: hsla(31deg, 100%, 76%, 1);\n  --md-primary-fg-color--dark: hsla(31deg, 100%, 66%, 1);\n  --md-primary-bg-color: var(--md-default-bg-color);\n  --md-primary-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=red],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=red] {\n  --md-code-link-accent-bg-color: #472c36;\n  --md-accent-fg-color: hsla(0deg, 100%, 62%, 1);\n  --md-accent-fg-color--transparent: hsla(0deg, 100%, 62%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=pink],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=pink] {\n  --md-code-link-accent-bg-color: #473149;\n  --md-accent-fg-color: hsla(326deg, 100%, 69%, 1);\n  --md-accent-fg-color--transparent: hsla(326deg, 100%, 69%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=purple],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=purple] {\n  --md-code-link-accent-bg-color: #3c3652;\n  --md-accent-fg-color: hsla(265deg, 89%, 73%, 1);\n  --md-accent-fg-color--transparent: hsla(265deg, 89%, 73%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=deep-purple],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=deep-purple] {\n  --md-code-link-accent-bg-color: #3c3652;\n  --md-accent-fg-color: hsla(265deg, 89%, 73%, 1);\n  --md-accent-fg-color--transparent: hsla(265deg, 89%, 73%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=blue],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=blue] {\n  --md-code-link-accent-bg-color: #2e3243;\n  --md-accent-fg-color: hsla(225deg, 27%, 46%, 1);\n  --md-accent-fg-color--transparent: hsla(225deg, 27%, 46%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=indigo],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=indigo] {\n  --md-code-link-accent-bg-color: #2e3243;\n  --md-accent-fg-color: hsla(225deg, 27%, 46%, 1);\n  --md-accent-fg-color--transparent: hsla(225deg, 27%, 46%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=light-blue],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=light-blue] {\n  --md-code-link-accent-bg-color: #2e3243;\n  --md-accent-fg-color: hsla(225deg, 27%, 46%, 1);\n  --md-accent-fg-color--transparent: hsla(225deg, 27%, 46%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=cyan],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=cyan] {\n  --md-code-link-accent-bg-color: #324553;\n  --md-accent-fg-color: hsla(191deg, 97%, 72%, 1);\n  --md-accent-fg-color--transparent: hsla(191deg, 97%, 72%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=teal],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=teal] {\n  --md-code-link-accent-bg-color: #324553;\n  --md-accent-fg-color: hsla(191deg, 97%, 72%, 1);\n  --md-accent-fg-color--transparent: hsla(191deg, 97%, 72%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=green],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=green] {\n  --md-code-link-accent-bg-color: #2a483d;\n  --md-accent-fg-color: hsla(135deg, 94%, 60%, 1);\n  --md-accent-fg-color--transparent: hsla(135deg, 94%, 60%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=light-green],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=light-green] {\n  --md-code-link-accent-bg-color: #2a483d;\n  --md-accent-fg-color: hsla(135deg, 94%, 60%, 1);\n  --md-accent-fg-color--transparent: hsla(135deg, 94%, 60%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=lime],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=lime] {\n  --md-code-link-accent-bg-color: #2a483d;\n  --md-accent-fg-color: hsla(135deg, 94%, 60%, 1);\n  --md-accent-fg-color--transparent: hsla(135deg, 94%, 60%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=yellow],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=yellow] {\n  --md-code-link-accent-bg-color: #45483e;\n  --md-accent-fg-color: hsla(65deg, 92%, 71%, 1);\n  --md-accent-fg-color--transparent: hsla(65deg, 92%, 71%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=amber],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=amber] {\n  --md-code-link-accent-bg-color: #45483e;\n  --md-accent-fg-color: hsla(65deg, 92%, 71%, 1);\n  --md-accent-fg-color--transparent: hsla(65deg, 92%, 71%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=orange],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=orange] {\n  --md-code-link-accent-bg-color: #473d39;\n  --md-accent-fg-color: hsla(31deg, 100%, 66%, 1);\n  --md-accent-fg-color--transparent: hsla(31deg, 100%, 66%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n[data-md-color-scheme=dracula][data-md-color-accent=deep-orange],\n[data-md-color-scheme=dracula] :not([data-md-color-scheme])[data-md-color-primary=deep-orange] {\n  --md-code-link-accent-bg-color: #473d39;\n  --md-accent-fg-color: hsla(31deg, 100%, 66%, 1);\n  --md-accent-fg-color--transparent: hsla(31deg, 100%, 66%, 0.1);\n  --md-accent-bg-color: var(--md-default-bg-color);\n  --md-accent-bg-color--light: var(--md-default-bg-color--light);\n}\n\n/* Normal colors */\n:root {\n  --md-heart: #ff5252;\n  --md-heart-big: #ff1744;\n  /* Dark mode colors */\n}\n:root :focus-visible {\n  outline-style: solid;\n}\n:root [data-md-color-scheme=dracula] {\n  --md-heart: hsl(326, 100%, 74%);\n  --md-heart-big: hsl(0, 100%, 67%);\n}\n\n.md-typeset h4 {\n  margin: 2em 0 1em;\n}\n.md-typeset a.source-link {\n  position: relative;\n  top: -0.6rem;\n  float: right;\n  color: var(--md-default-fg-color--lighter);\n  transition: color 125ms;\n}\n.md-typeset a.source-link:hover {\n  color: var(--md-accent-fg-color);\n}\n.md-typeset a.source-link .twemoji {\n  height: 1.2rem;\n}\n.md-typeset a.source-link .twemoji svg {\n  width: 1.2rem;\n  height: 1.2rem;\n}\n.md-typeset div.highlight.md-max-height pre > code {\n  max-height: 15rem;\n}\n\n.twemoji.heart-throb svg, .twemoji.heart-throb-hover svg {\n  position: relative;\n  color: var(--md-heart);\n  animation: pulse 1.5s ease infinite;\n}\n\n@keyframes pulse {\n  0% {\n    transform: scale(1);\n  }\n  40% {\n    color: var(--md-heart-big);\n    transform: scale(1.3);\n  }\n  50% {\n    transform: scale(1.2);\n  }\n  60% {\n    color: var(--md-heart-big);\n    transform: scale(1.3);\n  }\n  100% {\n    transform: scale(1);\n  }\n}\nfooter.sponsorship {\n  text-align: center;\n}\nfooter.sponsorship hr {\n  display: inline-block;\n  width: 1.6rem;\n  margin: 0 0.7rem;\n  vertical-align: middle;\n  border-bottom: 2px solid var(--md-default-fg-color--lighter);\n}\nfooter.sponsorship:hover hr {\n  border-color: var(--md-accent-fg-color);\n}\nfooter.sponsorship:not(:hover) .twemoji.heart-throb-hover svg {\n  color: var(--md-default-fg-color--lighter) !important;\n}\n\nbody:not([data-md-prefers-color-scheme=true])[data-md-color-scheme=dracula] .md-icon .light-mode,\nbody:not([data-md-prefers-color-scheme=true])[data-md-color-scheme=dracula] .md-icon .system-mode,\nbody:not([data-md-prefers-color-scheme=true])[data-md-color-scheme=dracula] .md-icon .unknown-mode {\n  display: none;\n}\nbody:not([data-md-prefers-color-scheme=true])[data-md-color-scheme=default] .md-icon .dark-mode,\nbody:not([data-md-prefers-color-scheme=true])[data-md-color-scheme=default] .md-icon .system-mode,\nbody:not([data-md-prefers-color-scheme=true])[data-md-color-scheme=default] .md-icon .unknown-mode {\n  display: none;\n}\nbody:not([data-md-prefers-color-scheme=true]):not([data-md-color-scheme=default]):not([data-md-color-scheme=dracula]) .md-icon .dark-mode,\nbody:not([data-md-prefers-color-scheme=true]):not([data-md-color-scheme=default]):not([data-md-color-scheme=dracula]) .md-icon .light-mode,\nbody:not([data-md-prefers-color-scheme=true]):not([data-md-color-scheme=default]):not([data-md-color-scheme=dracula]) .md-icon .system-mode {\n  display: none;\n}\nbody[data-md-prefers-color-scheme=true] .md-icon .dark-mode,\nbody[data-md-prefers-color-scheme=true] .md-icon .light-mode,\nbody[data-md-prefers-color-scheme=true] .md-icon .unknown-mode {\n  display: none;\n}\n\n.md-header-nav__scheme {\n  z-index: 0;\n}\n\n@media screen and (max-width: 59.9375em) {\n  .md-header-nav__scheme {\n    padding-right: 0;\n  }\n  label[for=__search] {\n    padding-left: 0;\n  }\n}\n[data-md-toggle=search]:checked ~ .md-header .md-header-nav__scheme {\n  display: none;\n}\n\n.md-typeset .admonition, .md-typeset details {\n  border-width: 0;\n  border-left-width: 4px;\n}\n\n/* Style new admonitions with dark or light colors */\n:root > * {\n  --md-admonition-bg-color: transparent;\n  --md-admonition-icon--settings: svg-load(\"@mdi/svg/svg/cog.svg\");\n  --md-admonition-bg-color--settings: rgba(170, 0, 255, 0.1);\n  --md-admonition-icon-color--settings: #aa00ff;\n  --md-admonition-shadow-color--settings: rgba(170, 0, 255, 0.1);\n  --md-admonition-icon--new: svg-load(\"@mdi/svg/svg/alert-decagram.svg\");\n  --md-admonition-bg-color--new: rgba(255, 214, 0, 0.1);\n  --md-admonition-icon-color--new: #ffd600;\n  --md-admonition-shadow-color--new: rgba(255, 214, 0, 0.1);\n  --md-admonition-bg-color--note: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--note: hsl(51, 94%, 73%);\n  --md-admonition-shadow-color--note: rgba(251, 231, 121, 0.1);\n  --md-admonition-bg-color--abstract: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--abstract: hsl(191, 97%, 77%);\n  --md-admonition-shadow-color--abstract: rgba(139, 232, 253, 0.1);\n  --md-admonition-bg-color--info: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--info: hsl(190, 94%, 87%);\n  --md-admonition-shadow-color--info: rgba(191, 243, 253, 0.1);\n  --md-admonition-bg-color--tip: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--tip: hsl(161, 97%, 77%);\n  --md-admonition-shadow-color--tip: rgba(139, 253, 217, 0.1);\n  --md-admonition-bg-color--success: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--success: hsl(135, 94%, 65%);\n  --md-admonition-shadow-color--success: rgba(82, 250, 124, 0.1);\n  --md-admonition-bg-color--question: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--question: hsl(135, 92%, 79%);\n  --md-admonition-shadow-color--question: rgba(152, 251, 177, 0.1);\n  --md-admonition-bg-color--warning: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--warning: hsl(31, 100%, 71%);\n  --md-admonition-shadow-color--warning: rgba(255, 184, 107, 0.1);\n  --md-admonition-bg-color--failure: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--failure: hsl(0, 100%, 59%);\n  --md-admonition-shadow-color--failure: rgba(255, 46, 46, 0.1);\n  --md-admonition-bg-color--danger: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--danger: hsl(0, 100%, 67%);\n  --md-admonition-shadow-color--danger: rgba(255, 87, 87, 0.1);\n  --md-admonition-bg-color--bug: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--bug: hsl(325, 100%, 64%);\n  --md-admonition-shadow-color--bug: rgba(255, 71, 179, 0.1);\n  --md-admonition-bg-color--example: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--example: hsl(265, 89%, 78%);\n  --md-admonition-shadow-color--example: rgba(191, 149, 249, 0.1);\n  --md-admonition-bg-color--quote: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--quote: hsl(225, 8%, 51%);\n  --md-admonition-shadow-color--quote: rgba(120, 125, 140, 0.1);\n}\n:root > *[data-md-color-scheme=dracula] {\n  --md-admonition-icon-color: $drac-dark-yellow;\n}\n:root > *[data-md-color-scheme=dracula] {\n  --md-admonition-bg-color--settings: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--settings: hsl(326, 100%, 74%);\n  --md-admonition-shadow-color--settings: rgba(255, 122, 198, 0.1);\n}\n:root > *[data-md-color-scheme=dracula] {\n  --md-admonition-bg-color--new: var(--md-default-bg-color--ultra-dark);\n  --md-admonition-icon-color--new: hsl(65, 92%, 76%);\n  --md-admonition-shadow-color--new: rgba(241, 250, 137, 0.1);\n}\n\n/* Dark mode changes */\n[data-md-color-scheme=dracula] .md-typeset .admonition, [data-md-color-scheme=dracula] .md-typeset details {\n  border-color: var(--md-admonition-icon-color--note);\n  box-shadow: var(--md-shadow-z2);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition:focus-within, [data-md-color-scheme=dracula] .md-typeset details:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--note);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details > summary {\n  background-color: var(--md-admonition-bg-color--note);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details > summary::before {\n  background-color: var(--md-admonition-icon-color--note);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details > summary::after {\n  color: var(--md-admonition-icon-color--note);\n}\n\n/* Style existing admonitions with dark mode colors */\n[data-md-color-scheme=dracula] .md-typeset .admonition.note, [data-md-color-scheme=dracula] .md-typeset details.note {\n  border-color: var(--md-admonition-icon-color--note);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.note:focus-within, [data-md-color-scheme=dracula] .md-typeset details.note:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--note);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.note > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.note > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.note > summary {\n  background-color: var(--md-admonition-bg-color--note);\n  border-color: var(--md-admonition-icon-color--note);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.note > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.note > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.note > summary::before {\n  background-color: var(--md-admonition-icon-color--note);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.note > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.note > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.note > summary::after {\n  color: var(--md-admonition-icon-color--note);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.abstract, [data-md-color-scheme=dracula] .md-typeset details.abstract {\n  border-color: var(--md-admonition-icon-color--abstract);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.abstract:focus-within, [data-md-color-scheme=dracula] .md-typeset details.abstract:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--abstract);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.abstract > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.abstract > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.abstract > summary {\n  background-color: var(--md-admonition-bg-color--abstract);\n  border-color: var(--md-admonition-icon-color--abstract);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.abstract > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.abstract > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.abstract > summary::before {\n  background-color: var(--md-admonition-icon-color--abstract);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.abstract > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.abstract > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.abstract > summary::after {\n  color: var(--md-admonition-icon-color--abstract);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.info, [data-md-color-scheme=dracula] .md-typeset details.info {\n  border-color: var(--md-admonition-icon-color--info);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.info:focus-within, [data-md-color-scheme=dracula] .md-typeset details.info:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--info);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.info > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.info > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.info > summary {\n  background-color: var(--md-admonition-bg-color--info);\n  border-color: var(--md-admonition-icon-color--info);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.info > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.info > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.info > summary::before {\n  background-color: var(--md-admonition-icon-color--info);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.info > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.info > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.info > summary::after {\n  color: var(--md-admonition-icon-color--info);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.tip, [data-md-color-scheme=dracula] .md-typeset details.tip {\n  border-color: var(--md-admonition-icon-color--tip);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.tip:focus-within, [data-md-color-scheme=dracula] .md-typeset details.tip:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--tip);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.tip > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.tip > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.tip > summary {\n  background-color: var(--md-admonition-bg-color--tip);\n  border-color: var(--md-admonition-icon-color--tip);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.tip > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.tip > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.tip > summary::before {\n  background-color: var(--md-admonition-icon-color--tip);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.tip > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.tip > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.tip > summary::after {\n  color: var(--md-admonition-icon-color--tip);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.success, [data-md-color-scheme=dracula] .md-typeset details.success {\n  border-color: var(--md-admonition-icon-color--success);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.success:focus-within, [data-md-color-scheme=dracula] .md-typeset details.success:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--success);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.success > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.success > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.success > summary {\n  background-color: var(--md-admonition-bg-color--success);\n  border-color: var(--md-admonition-icon-color--success);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.success > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.success > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.success > summary::before {\n  background-color: var(--md-admonition-icon-color--success);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.success > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.success > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.success > summary::after {\n  color: var(--md-admonition-icon-color--success);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.question, [data-md-color-scheme=dracula] .md-typeset details.question {\n  border-color: var(--md-admonition-icon-color--question);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.question:focus-within, [data-md-color-scheme=dracula] .md-typeset details.question:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--question);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.question > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.question > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.question > summary {\n  background-color: var(--md-admonition-bg-color--question);\n  border-color: var(--md-admonition-icon-color--question);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.question > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.question > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.question > summary::before {\n  background-color: var(--md-admonition-icon-color--question);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.question > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.question > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.question > summary::after {\n  color: var(--md-admonition-icon-color--question);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.warning, [data-md-color-scheme=dracula] .md-typeset details.warning {\n  border-color: var(--md-admonition-icon-color--warning);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.warning:focus-within, [data-md-color-scheme=dracula] .md-typeset details.warning:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--warning);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.warning > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.warning > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.warning > summary {\n  background-color: var(--md-admonition-bg-color--warning);\n  border-color: var(--md-admonition-icon-color--warning);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.warning > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.warning > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.warning > summary::before {\n  background-color: var(--md-admonition-icon-color--warning);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.warning > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.warning > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.warning > summary::after {\n  color: var(--md-admonition-icon-color--warning);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.failure, [data-md-color-scheme=dracula] .md-typeset details.failure {\n  border-color: var(--md-admonition-icon-color--failure);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.failure:focus-within, [data-md-color-scheme=dracula] .md-typeset details.failure:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--failure);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.failure > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.failure > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.failure > summary {\n  background-color: var(--md-admonition-bg-color--failure);\n  border-color: var(--md-admonition-icon-color--failure);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.failure > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.failure > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.failure > summary::before {\n  background-color: var(--md-admonition-icon-color--failure);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.failure > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.failure > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.failure > summary::after {\n  color: var(--md-admonition-icon-color--failure);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.danger, [data-md-color-scheme=dracula] .md-typeset details.danger {\n  border-color: var(--md-admonition-icon-color--danger);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.danger:focus-within, [data-md-color-scheme=dracula] .md-typeset details.danger:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--danger);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.danger > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.danger > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.danger > summary {\n  background-color: var(--md-admonition-bg-color--danger);\n  border-color: var(--md-admonition-icon-color--danger);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.danger > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.danger > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.danger > summary::before {\n  background-color: var(--md-admonition-icon-color--danger);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.danger > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.danger > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.danger > summary::after {\n  color: var(--md-admonition-icon-color--danger);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.bug, [data-md-color-scheme=dracula] .md-typeset details.bug {\n  border-color: var(--md-admonition-icon-color--bug);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.bug:focus-within, [data-md-color-scheme=dracula] .md-typeset details.bug:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--bug);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.bug > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.bug > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.bug > summary {\n  background-color: var(--md-admonition-bg-color--bug);\n  border-color: var(--md-admonition-icon-color--bug);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.bug > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.bug > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.bug > summary::before {\n  background-color: var(--md-admonition-icon-color--bug);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.bug > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.bug > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.bug > summary::after {\n  color: var(--md-admonition-icon-color--bug);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.example, [data-md-color-scheme=dracula] .md-typeset details.example {\n  border-color: var(--md-admonition-icon-color--example);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.example:focus-within, [data-md-color-scheme=dracula] .md-typeset details.example:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--example);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.example > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.example > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.example > summary {\n  background-color: var(--md-admonition-bg-color--example);\n  border-color: var(--md-admonition-icon-color--example);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.example > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.example > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.example > summary::before {\n  background-color: var(--md-admonition-icon-color--example);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.example > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.example > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.example > summary::after {\n  color: var(--md-admonition-icon-color--example);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.quote, [data-md-color-scheme=dracula] .md-typeset details.quote {\n  border-color: var(--md-admonition-icon-color--quote);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.quote:focus-within, [data-md-color-scheme=dracula] .md-typeset details.quote:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--quote);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.quote > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.quote > .admonition-title, [data-md-color-scheme=dracula] .md-typeset details.quote > summary {\n  background-color: var(--md-admonition-bg-color--quote);\n  border-color: var(--md-admonition-icon-color--quote);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.quote > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.quote > .admonition-title::before, [data-md-color-scheme=dracula] .md-typeset details.quote > summary::before {\n  background-color: var(--md-admonition-icon-color--quote);\n}\n[data-md-color-scheme=dracula] .md-typeset .admonition.quote > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.quote > .admonition-title::after, [data-md-color-scheme=dracula] .md-typeset details.quote > summary::after {\n  color: var(--md-admonition-icon-color--quote);\n}\n\n.md-typeset .admonition.settings, .md-typeset details.settings, .md-typeset .admonition.config, .md-typeset details.config {\n  border-color: var(--md-admonition-icon-color--settings);\n}\n.md-typeset .admonition.settings:focus-within, .md-typeset details.settings:focus-within, .md-typeset .admonition.config:focus-within, .md-typeset details.config:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--settings);\n}\n.md-typeset .admonition.settings > .admonition-title, .md-typeset details.settings > .admonition-title, .md-typeset details.settings > summary, .md-typeset .admonition.config > .admonition-title, .md-typeset details.config > .admonition-title, .md-typeset details.config > summary {\n  background-color: var(--md-admonition-bg-color--settings);\n  border-color: var(--md-admonition-icon-color--settings);\n}\n.md-typeset .admonition.settings > .admonition-title::before, .md-typeset details.settings > .admonition-title::before, .md-typeset details.settings > summary::before, .md-typeset .admonition.config > .admonition-title::before, .md-typeset details.config > .admonition-title::before, .md-typeset details.config > summary::before {\n  width: 1rem;\n  height: 1rem;\n  background-color: var(--md-admonition-icon-color--settings);\n  background-size: 1rem;\n  mask-image: var(--md-admonition-icon--settings);\n  content: \" \";\n}\n.md-typeset .admonition.settings > .admonition-title::after, .md-typeset details.settings > .admonition-title::after, .md-typeset details.settings > summary::after, .md-typeset .admonition.config > .admonition-title::after, .md-typeset details.config > .admonition-title::after, .md-typeset details.config > summary::after {\n  color: var(--md-admonition-icon-color--settings);\n}\n\n.md-typeset .admonition.new, .md-typeset details.new {\n  border-color: var(--md-admonition-icon-color--new);\n}\n.md-typeset .admonition.new:focus-within, .md-typeset details.new:focus-within {\n  box-shadow: 0 0 0 0.2rem var(--md-admonition-shadow-color--new);\n}\n.md-typeset .admonition.new > .admonition-title, .md-typeset details.new > .admonition-title, .md-typeset details.new > summary {\n  background-color: var(--md-admonition-bg-color--new);\n  border-color: var(--md-admonition-icon-color--new);\n}\n.md-typeset .admonition.new > .admonition-title::before, .md-typeset details.new > .admonition-title::before, .md-typeset details.new > summary::before {\n  width: 1rem;\n  height: 1rem;\n  background-color: var(--md-admonition-icon-color--new);\n  background-size: 1rem;\n  mask-image: var(--md-admonition-icon--new);\n  content: \" \";\n}\n.md-typeset .admonition.new > .admonition-title::after, .md-typeset details.new > .admonition-title::after, .md-typeset details.new > summary::after {\n  color: var(--md-admonition-icon-color--new);\n}\n\nmjx-container[display=true] {\n  font-size: 120% !important;\n}\n\nmjx-container:not([display]) {\n  font-size: 100% !important;\n}\n\n[data-md-color-scheme=slate],\n[data-md-color-scheme=dracula] {\n  /* stylelint-disable selector-class-pattern */\n  /* stylelint-enable selector-class-pattern */\n}\n[data-md-color-scheme=slate] .CtxtMenu_InfoSignature input,\n[data-md-color-scheme=slate] .CtxtMenu_InfoContent pre,\n[data-md-color-scheme=dracula] .CtxtMenu_InfoSignature input,\n[data-md-color-scheme=dracula] .CtxtMenu_InfoContent pre {\n  color: rgb(0, 0, 0);\n}\n[data-md-color-scheme=slate] .CtxtMenu_Info,\n[data-md-color-scheme=slate] .CtxtMenu_Menu,\n[data-md-color-scheme=dracula] .CtxtMenu_Info,\n[data-md-color-scheme=dracula] .CtxtMenu_Menu {\n  box-shadow: 0px 10px 20px rgba(0, 0, 0, 0.5);\n}\n\n.md-typeset .arithmatex {\n  overflow-x: auto !important;\n  overflow-y: hidden !important;\n}\n\n.katex-display .katex-html {\n  display: flex !important;\n  flex-direction: row;\n  flex-wrap: nowrap;\n  align-items: baseline;\n  justify-content: space-between;\n}\n.katex-display .katex-html .base {\n  display: inline !important;\n}\n.katex-display .katex-html .tag {\n  position: relative !important;\n  display: inline !important;\n  margin-left: var(--margin-small);\n}\n\n/* Don't use box shadows */\n.md-typeset del.critic,\n.md-typeset ins.critic,\n.md-typeset mark.critic {\n  padding: 0 0.25em;\n  color: unset;\n  box-shadow: none;\n}\n.md-typeset .critic.break {\n  margin: 0;\n}\n\n/* Inherit admonition style */\n.md-typeset details {\n  overflow: hidden;\n}\n.md-typeset details > summary:focus {\n  outline-style: none;\n}\n\n.highlight .kc {\n  color: var(--md-code-hl-constant-color);\n}\n.highlight .nc,\n.highlight .ne {\n  color: var(--md-code-hl-class-color);\n}\n.highlight .mb {\n  color: var(--md-code-hl-number-color);\n}\n.highlight .nb,\n.highlight .bp {\n  color: var(--md-code-hl-builtin-color);\n}\n.highlight .nn {\n  color: var(--md-code-hl-namespace-color);\n}\n.highlight .na,\n.highlight .nd,\n.highlight .ni {\n  color: var(--md-code-hl-entity-color);\n}\n.highlight .nl,\n.highlight .nt {\n  color: var(--md-code-hl-tag-color);\n}\n\n.md-typeset {\n  /* Allow code to look like code everywhere despite Material's current preference */\n  /* Code that is also a link */\n  /* Don't always like code breaking in table cells */\n  /* Special line number coloring for tables */\n}\n.md-typeset *:not(pre) > code {\n  margin: 0;\n  padding: 0 0.2941176471em;\n  color: var(--md-code-fg-color);\n  background-color: var(--md-code-inline-bg-color);\n  border-radius: 0.1rem;\n  box-shadow: none;\n}\n.md-typeset a > code {\n  color: inherit !important;\n  background-color: var(--md-code-link-bg-color) !important;\n  transition: color 125ms;\n  transition: background-color 125ms;\n  /* If we are linking highlighted, inline code, force it to just look like a code link */\n}\n.md-typeset a > code * {\n  color: var(--md-typeset-a-color) !important;\n}\n.md-typeset a > code:hover {\n  background-color: var(--md-code-link-accent-bg-color) !important;\n}\n.md-typeset a > code:hover * {\n  color: var(--md-accent-fg-color) !important;\n}\n.md-typeset pre > code {\n  outline: none;\n}\n.md-typeset td code {\n  word-break: normal;\n}\n.md-typeset .highlight {\n  /* Needed for tab preserving mode */\n  tab-size: 8;\n  /* `pymdownx-inline` mode */\n}\n.md-typeset .highlight + .result {\n  border-width: 0.1rem;\n}\n.md-typeset .highlight [data-linenos] {\n  /* Special line mode coloring */\n}\n.md-typeset .highlight [data-linenos].special::before {\n  background-color: var(--md-code-special-bg-color);\n}\n.md-typeset .highlighttable .linenodiv .special {\n  margin-right: -0.5882352941em;\n  margin-left: -1.1764705882em;\n  padding-right: 0.5882352941em;\n  padding-left: 1.1764705882em;\n  background-color: var(--md-code-special-bg-color);\n}\n.md-typeset .highlight span.filename {\n  position: relative;\n  display: block;\n  margin-top: 1em;\n  padding: 0.5em 1.1764705882em 0.5em 2.9411764706em;\n  font-weight: 700;\n  font-size: 0.68rem;\n  background-color: var(--md-code-title-bg-color);\n  border-top-left-radius: 0.1rem;\n  border-top-right-radius: 0.1rem;\n}\n.md-typeset .highlight span.filename + pre {\n  margin-top: 0;\n}\n.md-typeset .highlight span.filename + pre code {\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n}\n.md-typeset .highlight span.filename::before {\n  position: absolute;\n  left: 0.8823529412em;\n  width: 1.4705882353em;\n  height: 1.4705882353em;\n  background-color: var(--md-default-fg-color);\n  mask-image: svg-load(\"@mdi/svg/svg/console.svg\");\n  mask-repeat: no-repeat;\n  mask-size: contain;\n  content: \"\";\n}\n.md-typeset .collapse-code {\n  position: relative;\n  margin-top: 1em;\n  margin-bottom: 1em;\n}\n.md-typeset .collapse-code pre {\n  margin-top: 0;\n  margin-bottom: 0;\n}\n.md-typeset .collapse-code input {\n  display: none;\n}\n.md-typeset .collapse-code input ~ .code-footer {\n  width: 100%;\n  margin: 0;\n  padding: 0.25em 0.5em 0.25em 0em;\n}\n.md-typeset .collapse-code input ~ .code-footer label {\n  position: relative;\n  margin: 0.05em;\n  padding: 0.15em 0.8em;\n  color: var(--md-primary-bg-color);\n  font-size: 90%;\n  background-color: var(--md-primary-fg-color);\n  mask-repeat: no-repeat;\n  mask-size: contain;\n  border-radius: 0.1rem;\n  cursor: pointer;\n  content: \"\";\n}\n.md-typeset .collapse-code input ~ .code-footer label:hover {\n  background-color: var(--md-accent-fg-color);\n}\n.md-typeset .collapse-code input ~ .code-footer label::before {\n  position: absolute;\n  top: 0.15em;\n  left: 0.15em;\n  display: block;\n  box-sizing: border-box;\n  width: 1.25em;\n  height: 1.25em;\n  background-color: var(--md-primary-bg-color);\n  background-size: 1.25em;\n  content: \"\";\n}\n.md-typeset .collapse-code input ~ .code-footer label.expand {\n  display: none;\n}\n.md-typeset .collapse-code input ~ .code-footer label.expand::before {\n  mask-image: svg-load(\"@mdi/svg/svg/arrow-expand.svg\");\n}\n.md-typeset .collapse-code input ~ .code-footer label.collapse::before {\n  mask-image: svg-load(\"@mdi/svg/svg/arrow-collapse.svg\");\n}\n.md-typeset .collapse-code input:checked ~ .code-footer label.expand {\n  display: inline;\n}\n.md-typeset .collapse-code input:checked ~ .code-footer label.collapse {\n  display: none;\n}\n.md-typeset .collapse-code input:checked + div.highlight code {\n  max-height: 9.375em;\n  overflow: hidden;\n}\n.md-typeset .collapse-code input:checked ~ .code-footer {\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  padding: 2em 0.5em 0.5em 0.8rem;\n  background-image: linear-gradient(to bottom, transparent, var(--md-default-bg-color) 80% 100%);\n}\n@media screen and (max-width: 44.9375em) {\n  .md-typeset > diagram-div {\n    margin-right: -0.8rem;\n    margin-left: -0.8rem;\n  }\n  .md-typeset > .collapse-code {\n    margin-right: -0.8rem;\n    margin-left: -0.8rem;\n  }\n  .md-typeset > .collapse-code label.collapse {\n    left: 0.8rem;\n  }\n}\n\n.md-typeset .keys .key-power::before {\n  padding-right: 0.4em;\n  content: \"⏻\";\n}\n.md-typeset .keys .key-fingerprint::before {\n  padding-right: 0.4em;\n  content: \"☝\";\n}\n\n:root > * {\n  --magiclink-email-icon: svg-load(\"@mdi/svg/svg/mail.svg\");\n  --magiclink-github-icon: svg-load(\"@mdi/svg/svg/github.svg\");\n  --magiclink-bitbucket-icon: svg-load(\"@mdi/svg/svg/bitbucket.svg\");\n  --magiclink-gitlab-icon: svg-load(\"@mdi/svg/svg/gitlab.svg\");\n  --magiclink-commit-icon: svg-load(\"@primer/octicons/build/svg/git-commit-24.svg\");\n  --magiclink-compare-icon: svg-load(\"@primer/octicons/build/svg/file-diff-24.svg\");\n  --magiclink-pull-icon: svg-load(\"@primer/octicons/build/svg/git-pull-request-24.svg\");\n  --magiclink-issue-icon: svg-load(\"@primer/octicons/build/svg/issue-opened-24.svg\");\n  --magiclink-discussion-icon: svg-load(\"@primer/octicons/build/svg/comment-discussion-24.svg\");\n}\n\n.md-typeset a[href^=\"mailto:\"]:not(.magiclink-ignore)::before {\n  mask-image: var(--magiclink-email-icon);\n}\n.md-typeset .magiclink-repository:not(.magiclink-ignore),\n.md-typeset .magiclink-compare:not(.magiclink-ignore),\n.md-typeset .magiclink-commit:not(.magiclink-ignore),\n.md-typeset .magiclink-pull:not(.magiclink-ignore),\n.md-typeset .magiclink-issue:not(.magiclink-ignore),\n.md-typeset .magiclink-discussion:not(.magiclink-ignore),\n.md-typeset a[href^=\"mailto:\"]:not(.magiclink-ignore) {\n  position: relative;\n  padding-left: 1.375em;\n}\n.md-typeset .magiclink-repository:not(.magiclink-ignore)::before,\n.md-typeset .magiclink-compare:not(.magiclink-ignore)::before,\n.md-typeset .magiclink-commit:not(.magiclink-ignore)::before,\n.md-typeset .magiclink-pull:not(.magiclink-ignore)::before,\n.md-typeset .magiclink-issue:not(.magiclink-ignore)::before,\n.md-typeset .magiclink-discussion:not(.magiclink-ignore)::before,\n.md-typeset a[href^=\"mailto:\"]:not(.magiclink-ignore)::before {\n  position: absolute;\n  top: 0;\n  left: 0;\n  display: block;\n  box-sizing: border-box;\n  width: 1.25em;\n  height: 1.25em;\n  background-color: var(--md-typeset-a-color);\n  background-size: 1.25em;\n  transition: background-color 125ms;\n  mask-repeat: no-repeat;\n  mask-size: contain;\n  content: \"\";\n}\n.md-typeset .magiclink-repository:not(.magiclink-ignore):hover::before,\n.md-typeset .magiclink-compare:not(.magiclink-ignore):hover::before,\n.md-typeset .magiclink-commit:not(.magiclink-ignore):hover::before,\n.md-typeset .magiclink-pull:not(.magiclink-ignore):hover::before,\n.md-typeset .magiclink-issue:not(.magiclink-ignore):hover::before,\n.md-typeset .magiclink-discussion:not(.magiclink-ignore):hover::before,\n.md-typeset a[href^=\"mailto:\"]:not(.magiclink-ignore):hover::before {\n  background-color: var(--md-accent-fg-color);\n}\n\n.md-typeset .magiclink-commit:not(.magiclink-ignore)::before {\n  mask-image: var(--magiclink-commit-icon);\n}\n\n.md-typeset .magiclink-compare:not(.magiclink-ignore)::before {\n  mask-image: var(--magiclink-compare-icon);\n}\n\n.md-typeset .magiclink-pull:not(.magiclink-ignore)::before {\n  mask-image: var(--magiclink-pull-icon);\n}\n\n.md-typeset .magiclink-issue:not(.magiclink-ignore)::before {\n  mask-image: var(--magiclink-issue-icon);\n}\n\n.md-typeset .magiclink-discussion:not(.magiclink-ignore)::before {\n  mask-image: var(--magiclink-discussion-icon);\n}\n\n.md-typeset .magiclink-repository.magiclink-github:not(.magiclink-ignore)::before {\n  mask-image: var(--magiclink-github-icon);\n}\n\n.md-typeset .magiclink-repository.magiclink-gitlab:not(.magiclink-ignore)::before {\n  mask-image: var(--magiclink-gitlab-icon);\n}\n\n.md-typeset .magiclink-repository.magiclink-bitbucket:not(.magiclink-ignore)::before {\n  mask-image: var(--magiclink-bitbucket-icon);\n}\n\n/* Shadow boxes sometimes give issues, so just pad. */\n.md-typeset mark:not(.critic) {\n  box-shadow: none;\n}\n\n.md-typeset {\n  /* Progress Bars */\n  /* Stripe animation */\n}\n.md-typeset .progress-label {\n  position: absolute;\n  width: 100%;\n  margin: 0;\n  color: var(--md-text-color);\n  font-weight: 700;\n  line-height: 1.4rem;\n  white-space: nowrap;\n  text-align: center;\n  text-shadow: -0.0625em -0.0625em 0.375em var(--md-default-bg-color--light), 0.0625em -0.0625em 0.375em var(--md-default-bg-color--light), -0.0625em 0.0625em 0.375em var(--md-default-bg-color--light), 0.0625em 0.0625em 0.375em var(--md-default-bg-color--light);\n}\n.md-typeset .progress-bar {\n  float: left;\n  height: 1.2rem;\n  background-color: #2979ff;\n}\n.md-typeset .candystripe-animate .progress-bar {\n  animation: animate-stripes 3s linear infinite;\n}\n.md-typeset .progress {\n  position: relative;\n  display: block;\n  width: 100%;\n  height: 1.2rem;\n  margin: 0.5rem 0;\n  background-color: var(--md-default-fg-color--lightest);\n}\n.md-typeset .progress.thin {\n  height: 0.4rem;\n  margin-top: 0.9rem;\n}\n.md-typeset .progress.thin .progress-label {\n  margin-top: -0.4rem;\n}\n.md-typeset .progress.thin .progress-bar {\n  height: 0.4rem;\n}\n.md-typeset .progress.candystripe .progress-bar {\n  background-image: linear-gradient(135deg, var(--md-progress-stripe) 27%, transparent 27%, transparent 52%, var(--md-progress-stripe) 52%, var(--md-progress-stripe) 77%, transparent 77%, transparent);\n  background-size: 2rem 2rem;\n}\n.md-typeset .progress-100plus .progress-bar {\n  background-color: var(--md-progress-100);\n}\n.md-typeset .progress-80plus .progress-bar {\n  background-color: var(--md-progress-80);\n}\n.md-typeset .progress-60plus .progress-bar {\n  background-color: var(--md-progress-60);\n}\n.md-typeset .progress-40plus .progress-bar {\n  background-color: var(--md-progress-40);\n}\n.md-typeset .progress-20plus .progress-bar {\n  background-color: var(--md-progress-20);\n}\n.md-typeset .progress-0plus .progress-bar {\n  background-color: var(--md-progress-0);\n}\n@keyframes animate-stripes {\n  0% {\n    background-position: 0 0;\n  }\n  100% {\n    background-position: 6rem 0;\n  }\n}\n\n/* Dark mode changes */\n[data-md-color-scheme=dracula] .md-typeset .tabbed-set > .tabbed-labels {\n  box-shadow: 0 -0.05rem var(--md-default-fg-color--lighter) inset;\n}\n\n/* Style code blocks to fill full tab,\n   but otherwise, pad content. */\n.md-typeset .tabbed-alternate.tabbed-set .tabbed-control {\n  width: 2rem;\n}\n.md-typeset .tabbed-alternate.tabbed-set .tabbed-control[hidden] {\n  width: 1.2rem;\n  opacity: 0;\n}\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block {\n  padding: 0 0.6rem;\n}\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > pre:only-child,\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > .codehilite:only-child,\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > .codehilitetable:only-child,\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > .highlight:only-child,\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > .highlighttable:only-child {\n  margin-right: -1.2rem;\n  margin-left: -1.2rem;\n  padding-right: 0.6rem;\n  padding-left: 0.6rem;\n}\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > pre:only-child span.filename,\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > .codehilite:only-child span.filename,\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > .codehilitetable:only-child span.filename,\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > .highlight:only-child span.filename,\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > .highlighttable:only-child span.filename {\n  margin-top: 0;\n}\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > .collapse-code:only-child {\n  margin-top: 0;\n  margin-right: -1.2rem;\n  margin-left: -1.2rem;\n  padding-right: 0.6rem;\n  padding-left: 0.6rem;\n}\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > .collapse-code:only-child > .code-footer {\n  left: 0.6rem;\n}\n.md-typeset .tabbed-alternate.tabbed-set > .tabbed-content > .tabbed-block > diagram-div:only-child {\n  margin-right: -1.2rem;\n  margin-left: -1.2rem;\n  padding-right: 0.6rem;\n  padding-left: 0.6rem;\n}\n\n.js .md-typeset .tabbed-labels::before {\n  background-color: var(--md-accent-fg-color);\n}\n\n/* Ignore mobile overflow styling that extends the tab bar */\n@media screen and (max-width: 44.9375em) {\n  [dir=ltr] .md-content__inner > .tabbed-set .tabbed-labels {\n    padding-left: 0;\n  }\n  .md-content__inner > .tabbed-set .tabbed-labels {\n    max-width: 100%;\n    margin: 0;\n    padding-inline-start: 0;\n    scroll-padding-inline-start: 0;\n  }\n  .md-content__inner > .tabbed-set .tabbed-labels::after {\n    padding-inline-end: 0;\n    content: none;\n  }\n  .md-content__inner > .tabbed-set .tabbed-labels ~ .tabbed-control--prev {\n    margin-inline-start: 0;\n    padding-inline-start: 0;\n  }\n  .md-content__inner > .tabbed-set .tabbed-labels ~ .tabbed-control--next {\n    margin-inline-end: 0;\n    padding-inline-end: 0;\n  }\n}\n/* Dark mode changes */\n[data-md-color-scheme=dracula] .md-typeset table:not([class]) {\n  box-shadow: var(--md-shadow-z2);\n}\n[data-md-color-scheme=dracula] .md-typeset table:not([class]) tr:hover {\n  background-color: rgba(0, 0, 0, 0.08);\n}\n[data-md-color-scheme=dracula] .md-typeset table:not([class]) th {\n  color: var(--md-text-color);\n  background-color: var(--md-default-bg-color--ultra-dark);\n  border-bottom: 0.05rem solid var(--md-primary-fg-color);\n}\n[data-md-color-scheme=dracula] .md-typeset table:not([class]) td {\n  border-top: 0.05rem solid var(--md-default-fg-color--lighter);\n}\n\n/* Dark mode changes */\n[data-md-color-scheme=dracula] .md-typeset .task-list-control .task-list-indicator::before {\n  background-color: var(--md-default-fg-color--lighter);\n}\n[data-md-color-scheme=dracula] .md-typeset .task-list-control [type=checkbox]:checked + .task-list-indicator::before {\n  background-color: hsl(135, 94%, 65%);\n}\n\n.md-typeset .headerlink {\n  width: 1em;\n  height: 1em;\n  vertical-align: middle;\n  background-color: var(--md-default-fg-color--lighter);\n  background-size: 1em;\n  mask-size: 1em;\n  mask-repeat: no-repeat;\n  visibility: visible;\n  mask-image: svg-load(\"@mdi/svg/svg/link.svg\");\n}\n.md-typeset .headerlink:hover,\n.md-typeset [id]:target .headerlink {\n  background-color: var(--md-accent-fg-color);\n}\n\n/* Special handling of Mermaid content. */\ndiagram-div {\n  overflow: auto;\n}\n\nhtml {\n  background-color: transparent;\n}\n\n[data-md-component=announce] .twemoji {\n  color: var(--md-primary-fg-color);\n}\n\n/* Dark mode changes */\n[data-md-color-scheme=dracula] {\n  --md-text-color: var(--md-default-fg-color);\n  background-color: var(--md-default-bg-color);\n  --md-footer-bg-color: transparent;\n  --md-footer-bg-color--dark: var(--md-default-bg-color--darkest);\n  --md-header-fg-color: var(--md-text-color);\n  --md-header-bg-color: var(--md-default-bg-color--darkest);\n}\n[data-md-color-scheme=dracula] .md-header {\n  color: var(--md-text-color);\n  background-color: var(--md-header-bg-color);\n  border-bottom: 0.05rem solid var(--md-primary-fg-color);\n}\n[data-md-color-scheme=dracula] .md-header[data-md-state=shadow] {\n  box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0.15), 0 0 0.2rem 0.4rem rgba(0, 0, 0, 0.2);\n}\n[data-md-color-scheme=dracula] .md-top {\n  background-color: var(--md-default-bg-color--dark);\n}\n[data-md-color-scheme=dracula] .md-top:hover {\n  background-color: var(--md-primary-fg-color);\n}\n[data-md-color-scheme=dracula] .md-tabs {\n  color: var(--md-text-color);\n  background-color: var(--md-primary-fg-color--transparent);\n}\n[data-md-color-scheme=dracula] .md-tabs__link--active {\n  color: var(--md-primary-fg-color);\n}\n[data-md-color-scheme=dracula] .md-tabs__link:hover {\n  color: var(--md-accent-fg-color);\n}\n[data-md-color-scheme=dracula] .md-hero {\n  color: var(--md-text-color);\n  background-color: var(--md-primary-fg-color--transparent);\n}\n[data-md-color-scheme=dracula] .md-nav__source {\n  color: var(--md-text-color);\n}\n[data-md-color-scheme=dracula] .md-nav__link[data-md-state=blur] {\n  color: var(--md-default-fg-color--light);\n}\n[data-md-color-scheme=dracula] .md-nav__item .md-nav__link--active {\n  color: var(--md-primary-fg-color);\n}\n[data-md-color-scheme=dracula] .md-nav__link:focus, [data-md-color-scheme=dracula] .md-nav__link:hover {\n  color: var(--md-accent-fg-color);\n}\n@media screen and (max-width: 76.1875em) {\n  [data-md-color-scheme=dracula] .md-nav--primary .md-nav__item--active > .md-nav__link:not(:hover) {\n    color: var(--md-primary-fg-color);\n  }\n  [data-md-color-scheme=dracula] .md-nav--primary .md-nav__title {\n    color: var(--md-text-color);\n    background-color: var(--md-header-bg-color);\n    border-bottom: 0.05rem solid var(--md-primary-fg-color);\n  }\n}\n@media screen and (max-width: 59.9375em) {\n  [data-md-color-scheme=dracula] .md-nav__source {\n    color: var(--md-text-color);\n    background-color: var(--md-primary-fg-color--transparent);\n  }\n  [data-md-color-scheme=dracula] .md-nav .md-nav__title {\n    color: var(--md-text-color);\n    background-color: var(--md-header-bg-color);\n    border-bottom: 0.05rem solid var(--md-primary-fg-color);\n  }\n}\n[data-md-color-scheme=dracula] .md-search__input {\n  color: var(--md-text-color);\n  background-color: var(--md-accent-bg-color--light);\n}\n[data-md-color-scheme=dracula] .md-search__input:hover {\n  background-color: var(--md-default-bg-color--light);\n}\n[data-md-color-scheme=dracula] .md-search__input ~ .md-search__icon {\n  color: var(--md-text-color);\n}\n[data-md-color-scheme=dracula] .md-search__input::placeholder {\n  color: var(--md-default-fg-color--light);\n}\n[data-md-color-scheme=dracula] [data-md-toggle=search]:checked ~ .md-header .md-search__input {\n  background-color: transparent;\n}\n[data-md-color-scheme=dracula] .md-search__suggest {\n  color: var(--md-default-autocomplete-fg-color);\n}\n[data-md-color-scheme=dracula] .md-search__overlay, [data-md-color-scheme=dracula] .md-overlay {\n  background-color: var(--md-default-bg-color--light);\n}\n[data-md-color-scheme=dracula] .md-footer-nav__direction {\n  color: var(--md-primary-fg-color);\n}\n[data-md-color-scheme=dracula] .md-footer-meta {\n  border-top: 0.05rem solid var(--md-primary-fg-color);\n}\n[data-md-color-scheme=dracula] [data-md-component=announce] {\n  background-color: var(--md-default-bg-color--ultra-dark);\n}\n\n/* Don't force capitalization of `H5` elements. */\n.md-typeset h5 {\n  color: var(--md-text-color);\n  text-transform: none;\n}\n\n.md-typeset__scrollwrap,\n.md-sidebar__scrollwrap,\n.md-search__scrollwrap,\n.md-typeset pre > code,\n.md-typeset div.mermaid,\n.md-typeset div.diagram,\n.md-typeset mermaid-div,\n.md-typeset diagram-div,\n.md-typeset pre.arithmatex,\n.md-typeset div.arithmatex {\n  scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n  scrollbar-width: thin;\n}\n.md-typeset__scrollwrap::-webkit-scrollbar,\n.md-sidebar__scrollwrap::-webkit-scrollbar,\n.md-search__scrollwrap::-webkit-scrollbar,\n.md-typeset pre > code::-webkit-scrollbar,\n.md-typeset div.mermaid::-webkit-scrollbar,\n.md-typeset div.diagram::-webkit-scrollbar,\n.md-typeset mermaid-div::-webkit-scrollbar,\n.md-typeset diagram-div::-webkit-scrollbar,\n.md-typeset pre.arithmatex::-webkit-scrollbar,\n.md-typeset div.arithmatex::-webkit-scrollbar {\n  width: 0.2rem;\n  height: 0.2rem;\n}\n.md-typeset__scrollwrap::-webkit-scrollbar-corner,\n.md-sidebar__scrollwrap::-webkit-scrollbar-corner,\n.md-search__scrollwrap::-webkit-scrollbar-corner,\n.md-typeset pre > code::-webkit-scrollbar-corner,\n.md-typeset div.mermaid::-webkit-scrollbar-corner,\n.md-typeset div.diagram::-webkit-scrollbar-corner,\n.md-typeset mermaid-div::-webkit-scrollbar-corner,\n.md-typeset diagram-div::-webkit-scrollbar-corner,\n.md-typeset pre.arithmatex::-webkit-scrollbar-corner,\n.md-typeset div.arithmatex::-webkit-scrollbar-corner {\n  background-color: transparent;\n}\n.md-typeset__scrollwrap::-webkit-scrollbar-thumb,\n.md-sidebar__scrollwrap::-webkit-scrollbar-thumb,\n.md-search__scrollwrap::-webkit-scrollbar-thumb,\n.md-typeset pre > code::-webkit-scrollbar-thumb,\n.md-typeset div.mermaid::-webkit-scrollbar-thumb,\n.md-typeset div.diagram::-webkit-scrollbar-thumb,\n.md-typeset mermaid-div::-webkit-scrollbar-thumb,\n.md-typeset diagram-div::-webkit-scrollbar-thumb,\n.md-typeset pre.arithmatex::-webkit-scrollbar-thumb,\n.md-typeset div.arithmatex::-webkit-scrollbar-thumb {\n  background-color: var(--md-default-fg-color--lighter);\n}\n.md-typeset__scrollwrap::-webkit-scrollbar-thumb:hover,\n.md-sidebar__scrollwrap::-webkit-scrollbar-thumb:hover,\n.md-search__scrollwrap::-webkit-scrollbar-thumb:hover,\n.md-typeset pre > code::-webkit-scrollbar-thumb:hover,\n.md-typeset div.mermaid::-webkit-scrollbar-thumb:hover,\n.md-typeset div.diagram::-webkit-scrollbar-thumb:hover,\n.md-typeset mermaid-div::-webkit-scrollbar-thumb:hover,\n.md-typeset diagram-div::-webkit-scrollbar-thumb:hover,\n.md-typeset pre.arithmatex::-webkit-scrollbar-thumb:hover,\n.md-typeset div.arithmatex::-webkit-scrollbar-thumb:hover {\n  background-color: var(--md-accent-fg-color);\n}\n.md-typeset__scrollwrap:hover,\n.md-sidebar__scrollwrap:hover,\n.md-search__scrollwrap:hover,\n.md-typeset pre > code:hover,\n.md-typeset div.mermaid:hover,\n.md-typeset div.diagram:hover,\n.md-typeset mermaid-div:hover,\n.md-typeset diagram-div:hover,\n.md-typeset pre.arithmatex:hover,\n.md-typeset div.arithmatex:hover {\n  scrollbar-color: var(--md-accent-fg-color) transparent;\n}", "/* Normal colors */\n:root {\n  --md-heart: #{$clr-red-a200};\n  --md-heart-big: #{$clr-red-a400};\n\n  :focus-visible {\n    outline-style: solid;\n  }\n\n  /* Dark mode colors */\n  [data-md-color-scheme=\"dracula\"] {\n    --md-heart: #{$drac-pink};\n    --md-heart-big: #{$drac-red};\n  }\n}\n\n.md-typeset {\n\n    h4 {\n      margin: 2.0em 0 1em;\n    }\n\n    a.source-link {\n        position: relative;\n        top: px2rem(-12px);\n        float: right;\n        color: var(--md-default-fg-color--lighter);\n        transition: color 125ms;\n\n        &:hover {\n          color: var(--md-accent-fg-color);\n        }\n\n        .twemoji {\n            height: px2rem(24px);\n\n            svg {\n                width: px2rem(24px);\n                height: px2rem(24px);\n            }\n        }\n    }\n\n    div.highlight.md-max-height pre > code {\n      max-height: px2rem(300px);\n    }\n}\n\n.twemoji {\n  &.heart-throb, &.heart-throb-hover {\n    svg {\n      position: relative;\n      color: var(--md-heart);\n      animation: pulse 1.5s ease infinite;\n    }\n  }\n}\n\n@keyframes pulse {\n  0% { transform: scale(1); }\n  40% { color: var(--md-heart-big); transform: scale(1.3); }\n  50% { transform: scale(1.2); }\n  60% { color: var(--md-heart-big); transform: scale(1.3); }\n  100% { transform: scale(1); }\n}\n\nfooter.sponsorship {\n  text-align: center;\n\n  hr {\n    display: inline-block;\n    width: px2rem(32px);\n    margin: 0 px2rem(14px);\n    vertical-align: middle;\n    border-bottom: 2px solid var(--md-default-fg-color--lighter);\n  }\n\n  &:hover {\n    hr {\n        border-color: var(--md-accent-fg-color);\n    }\n  }\n\n  &:not(:hover) {\n    .twemoji.heart-throb-hover svg{\n        color: var(--md-default-fg-color--lighter) !important;\n    }\n  }\n}\n\n// Scheme toggle\nbody {\n  &:not([data-md-prefers-color-scheme=\"true\"])[data-md-color-scheme=\"dracula\"] .md-icon {\n    .light-mode,\n    .system-mode,\n    .unknown-mode {\n      display:  none;\n    }\n  }\n\n\n  &:not([data-md-prefers-color-scheme=\"true\"])[data-md-color-scheme=\"default\"] .md-icon {\n    .dark-mode,\n    .system-mode,\n    .unknown-mode {\n      display:  none;\n    }\n  }\n\n  &:not([data-md-prefers-color-scheme=\"true\"]):not([data-md-color-scheme=\"default\"]):not([data-md-color-scheme=\"dracula\"]) .md-icon {\n    .dark-mode,\n    .light-mode,\n    .system-mode {\n      display:  none;\n    }\n  }\n\n  &[data-md-prefers-color-scheme=\"true\"] .md-icon {\n    .dark-mode,\n    .light-mode,\n    .unknown-mode {\n      display: none;\n    }\n  }\n}\n\n.md-header-nav__scheme {\n  z-index: 0;\n}\n\n@include break-to-device(tablet portrait) {\n  .md-header-nav__scheme {\n    padding-right: 0;\n  }\n\n  label[for=\"__search\"] {\n    padding-left: 0;\n  }\n}\n\n[data-md-toggle=search]:checked~.md-header .md-header-nav__scheme {\n  display: none;\n}\n", ".md-typeset .admonition {\n  border-width: 0;\n  border-left-width: 4px;\n}\n\n$new-admonitions: (\n  settings config: $drac-pink $clr-purple-a700 \"@mdi/svg/svg/cog.svg\",\n  new: $drac-yellow $clr-yellow-a700 \"@mdi/svg/svg/alert-decagram.svg\"\n) !default;\n\n  $old-admonitions: (\n    note:                       $drac-dark-yellow,\n    abstract:                   $drac-cyan,\n    info:                       $drac-light-blue,\n    tip:                        $drac-teal,\n    success:                    $drac-green,\n    question:                   $drac-light-green,\n    warning:                    $drac-orange,\n    failure:                    $drac-dark-red,\n    danger:                     $drac-red,\n    bug:                        $drac-dark-pink,\n    example:                    $drac-purple,\n    quote:                      $drac-grey\n  ) !default;\n\n/* Style new admonitions with dark or light colors */\n:root > * {\n  --md-admonition-bg-color: transparent;\n\n  &[data-md-color-scheme=\"dracula\"] {\n    --md-admonition-icon-color: $drac-dark-yellow;\n  }\n\n  @each $names, $prop in $new-admonitions {\n    $tint: nth($prop, 1);\n    $tint2: nth($prop, 2);\n    $name: nth($names, 1);\n\n    --md-admonition-icon--#{$name}: svg-load(\"#{nth($prop, 3)}\");\n    --md-admonition-bg-color--#{$name}: #{transparentize($tint2, 0.9)};\n    --md-admonition-icon-color--#{$name}: #{$tint2};\n    --md-admonition-shadow-color--#{$name}: #{transparentize($tint2, 0.9)};\n\n    &[data-md-color-scheme=\"dracula\"] {\n      --md-admonition-bg-color--#{$name}: var(--md-default-bg-color--ultra-dark);\n      --md-admonition-icon-color--#{$name}: #{$tint};\n      --md-admonition-shadow-color--#{$name}: #{transparentize($tint, 0.9)};\n    }\n  }\n\n  @each $names, $tint in $old-admonitions {\n    $name: nth($names, 1);\n    --md-admonition-bg-color--#{$name}: var(--md-default-bg-color--ultra-dark);\n    --md-admonition-icon-color--#{$name}: #{$tint};\n    --md-admonition-shadow-color--#{$name}: #{transparentize($tint, 0.9)};\n  }\n}\n\n/* Dark mode changes */\n[data-md-color-scheme=\"dracula\"] .md-typeset .admonition {\n  border-color: var(--md-admonition-icon-color--note);\n\n  &:focus-within {\n    box-shadow: 0 0 0 px2rem(4px) var(--md-admonition-shadow-color--note);\n  }\n\n  > .admonition-title {\n    background-color: var(--md-admonition-bg-color--note);\n    &::before {\n      background-color: var(--md-admonition-icon-color--note);\n    }\n    &::after {\n      color: var(--md-admonition-icon-color--note);\n    }\n  }\n\n  box-shadow: var(--md-shadow-z2);\n}\n\n/* Style existing admonitions with dark mode colors */\n[data-md-color-scheme=\"dracula\"] {\n\n  @each $names, $prop in $old-admonitions {\n\n    $name: nth($names, 1);\n\n    // Define base class\n    .md-typeset .admonition.#{$name} {\n      border-color: var(--md-admonition-icon-color--#{$name});\n\n      &:focus-within {\n        box-shadow: 0 0 0 px2rem(4px) var(--md-admonition-shadow-color--#{$name});\n      }\n\n      // Define base class\n      > .admonition-title {\n        background-color: var(--md-admonition-bg-color--#{$name});\n        border-color: var(--md-admonition-icon-color--#{$name});\n\n        // Icon\n        &::before {\n          background-color: var(--md-admonition-icon-color--#{$name});\n        }\n        &::after {\n          color: var(--md-admonition-icon-color--#{$name});\n        }\n      }\n    }\n\n    // Define synonyms for base class\n    @if length($names) > 1 {\n      @for $n from 2 through length($names) {\n        .#{nth($names, $n)} {\n          @extend .#{$name};\n        }\n      }\n    }\n  }\n}\n\n@each $names, $prop in $new-admonitions {\n  $name: nth($names, 1);\n\n    // Define base class\n  .md-typeset .admonition.#{$name} {\n    border-color: var(--md-admonition-icon-color--#{$name});\n\n    &:focus-within {\n      box-shadow: 0 0 0 px2rem(4px) var(--md-admonition-shadow-color--#{$name});\n    }\n\n    > .admonition-title {\n      background-color: var(--md-admonition-bg-color--#{$name});\n      border-color: var(--md-admonition-icon-color--#{$name});\n\n      // Icon\n      &::before {\n        width: px2rem(20px);\n        height: px2rem(20px);\n        background-color: var(--md-admonition-icon-color--#{$name});\n        background-size: px2rem(20px);\n        mask-image: var(--md-admonition-icon--#{$name});\n        content: \"\\a0\";\n      }\n      &::after {\n        color: var(--md-admonition-icon-color--#{$name});\n      }\n    }\n  }\n\n  // Define synonyms for base class\n  @if length($names) > 1 {\n    @for $n from 2 through length($names) {\n      .#{nth($names, $n)} {\n        @extend .#{$name};\n      }\n    }\n  }\n}\n", "mjx-container[display=true] {\n  font-size: 120% !important;\n}\n\nmjx-container:not([display]) {\n  font-size: 100% !important;\n}\n\n[data-md-color-scheme=\"slate\"],\n[data-md-color-scheme=\"dracula\"] {\n  /* stylelint-disable selector-class-pattern */\n  .CtxtMenu_InfoSignature input,\n  .CtxtMenu_InfoContent pre {\n    color: rgb(0, 0, 0);\n  }\n  .CtxtMenu_Info,\n  .CtxtMenu_Menu {\n    box-shadow: 0px 10px 20px rgb(0 0 0 / 0.5);\n  }\n  /* stylelint-enable selector-class-pattern */\n}\n\n.md-typeset .arithmatex {\n  overflow-x: auto !important;\n  overflow-y: hidden !important;\n}\n\n// Fix tag overlap in `KaTeX`\n.katex-display {\n  .katex-html {\n    display: flex !important;\n    flex-direction: row;\n    flex-wrap: nowrap;\n    align-items: baseline;\n    justify-content: space-between;\n    // overflow-x: auto;\n\n    .base {\n      display: inline !important;\n    }\n\n    .tag {\n      position: relative !important;\n      display: inline !important;\n      margin-left: var(--margin-small);\n    }\n  }\n}\n", "/* Don't use box shadows */\n.md-typeset {\n  del,\n  ins,\n  mark {\n    &.critic {\n      padding: 0 px2em(4px, 16px);\n      color: unset;\n      box-shadow: none;\n    }\n  }\n\n  .critic.break {\n    margin: 0;\n  }\n}\n", "/* Inherit admonition style */\n.md-typeset {\n  details {\n    @extend .admonition;\n\n    overflow: hidden;\n\n    // Title\n    > summary {\n      @extend .admonition-title;\n\n      // Disable accessibility focus\n      &:focus {\n        outline-style: none;\n      }\n    }\n  }\n}\n", ".highlight {\n  .kc { // Keyword constant\n    color: var(--md-code-hl-constant-color);\n  }\n\n  .nc,  // Name, class\n  .ne,  // Name, exception\n  {\n    color: var(--md-code-hl-class-color);\n  }\n\n  .mb {\n    color: var(--md-code-hl-number-color);\n  }\n\n  .nb,  // Name, builtin\n  .bp { // Name, builtin pseudo\n    color: var(--md-code-hl-builtin-color);\n  }\n\n  .nn { // Name, namespace Name, namespace Name, namespace Name, namespace\n    color: var(--md-code-hl-namespace-color);\n  }\n\n  .na,  // Name, attribute\n  .nd,  // Name, decorator\n  .ni { // Name, entity\n    color: var(--md-code-hl-entity-color);\n  }\n  .nl,  // Name, label\n  .nt { // Name, tag\n    color: var(--md-code-hl-tag-color);\n  }\n}\n\n.md-typeset {\n\n  /* Allow code to look like code everywhere despite Material's current preference */\n  *:not(pre) > code {\n      margin: 0;\n      padding: 0 px2em( 4px, 13.6px);\n      color: var(--md-code-fg-color);\n      background-color: var(--md-code-inline-bg-color);\n      border-radius: px2rem(2px);\n      box-shadow: none;\n  }\n\n  /* Code that is also a link */\n  a {\n    > code {\n      color: inherit !important;\n      background-color: var(--md-code-link-bg-color) !important;\n      transition: color 125ms;\n      transition: background-color 125ms;\n\n      /* If we are linking highlighted, inline code, force it to just look like a code link */\n      * {\n        color: var(--md-typeset-a-color) !important;\n      }\n\n      &:hover {\n        background-color: var(--md-code-link-accent-bg-color) !important;\n\n        * {\n          color: var(--md-accent-fg-color) !important;\n        }\n      }\n    }\n  }\n\n  pre > code {\n    outline: none;\n  }\n\n  /* Don't always like code breaking in table cells */\n  td code {\n    word-break: normal;\n  }\n\n  .highlight {\n\n    /* Needed for tab preserving mode */\n    tab-size: 8;\n\n    + .result {\n      border-width: px2rem(2px);\n    }\n\n    /* `pymdownx-inline` mode */\n    [data-linenos] {\n\n      /* Special line mode coloring */\n      &.special::before {\n        background-color: var(--md-code-special-bg-color);\n      }\n    }\n  }\n\n    /* Special line number coloring for tables */\n  .highlighttable {\n    .linenodiv {\n      .special {\n        margin-right: px2em(-8px, 13.6px);\n        margin-left: px2em(-16px, 13.6px);\n        padding-right: px2em(8px, 13.6px);\n        padding-left: px2em(16px, 13.6px);\n        background-color: var(--md-code-special-bg-color);\n      }\n    }\n  }\n\n  // Filename span\n  .highlight span.filename {\n\n    // Adjust margins and and general container look of code block\n    + pre {\n      margin-top: 0;\n\n      code {\n        border-top-left-radius: 0;\n        border-top-right-radius: 0;\n      }\n    }\n\n    // Style the filename banner\n    position: relative;\n    display: block;\n    margin-top: 1em;\n    padding: px2em(8px) px2em(16px, 13.6px) px2em(8px) px2em(40px, 13.6px);\n    font-weight: 700;\n    font-size: px2rem(13.6px);\n    background-color: var(--md-code-title-bg-color);\n    border-top-left-radius: px2rem(2px);\n    border-top-right-radius: px2rem(2px);\n\n    // Add code icon\n    &::before {\n      position: absolute;\n      left: px2em(12px, 13.6px);\n      width: px2em(20px, 13.6px);\n      height: px2em(20px, 13.6px);\n      background-color: var(--md-default-fg-color);\n      mask-image: svg-load(\"@mdi/svg/svg/console.svg\");\n      mask-repeat: no-repeat;\n      mask-size: contain;\n      content: \"\";\n    }\n  }\n\n  .collapse-code {\n    position: relative;\n    margin-top: 1em;\n    margin-bottom: 1em;\n\n    pre {\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n\n    input{\n      display: none;\n\n      ~ .code-footer {\n        width: 100%;\n        margin: 0;\n        padding: px2em(4px) px2em(8px) px2em(4px) px2em(0px);\n\n        label {\n          position: relative;\n          margin: 0.05em;\n          padding: 0.15em 0.8em;\n          color: var(--md-primary-bg-color);\n          font-size: 90%;\n          background-color: var(--md-primary-fg-color);\n          mask-repeat: no-repeat;\n          mask-size: contain;\n          border-radius: px2rem(2px);\n          cursor: pointer;\n          content: \"\";\n\n          &:hover {\n            background-color: var(--md-accent-fg-color);\n          }\n\n          &::before {\n            position: absolute;\n            top: 0.15em;\n            left: 0.15em;\n            display: block;\n            box-sizing: border-box;\n            width: 1.25em;\n            height: 1.25em;\n            background-color: var(--md-primary-bg-color);\n            background-size: 1.25em;\n            content: \"\";\n          }\n\n          &.expand {\n            display: none;\n\n            &::before {\n              mask-image: svg-load(\"@mdi/svg/svg/arrow-expand.svg\");\n            }\n          }\n          &.collapse::before {\n            mask-image: svg-load(\"@mdi/svg/svg/arrow-collapse.svg\");\n          }\n        }\n      }\n\n      &:checked {\n        ~ .code-footer label.expand {\n          display: inline;\n        }\n\n        ~ .code-footer label.collapse {\n          display: none;\n        }\n\n        + div.highlight code {\n          max-height: px2em(150px);\n          overflow: hidden;\n        }\n\n        ~ .code-footer {\n          position: absolute;\n          bottom: 0;\n          left: 0;\n          padding: px2em(32px) px2em(8px) px2em(8px) px2rem(16px);\n          background-image: linear-gradient(to bottom,\n              transparent,\n              var(--md-default-bg-color) 80%\n          100%);\n        }\n      }\n    }\n  }\n\n  @include break-to-device(mobile) {\n\n    > {\n      diagram-div {\n        margin-right: px2rem(-16px);\n        margin-left: px2rem(-16px);\n      }\n      .collapse-code {\n        margin-right: px2rem(-16px);\n        margin-left: px2rem(-16px);\n\n        label.collapse {\n          left: px2rem(16px);\n        }\n      }\n    }\n  }\n}\n", ".md-typeset {\n\n  // Keyboard key\n  .keys {\n\n    // Define keyboard keys with left icon\n    @each $name, $code in (\n      // Extra Keys\n      \"power\":         \"\\23FB\",\n      \"fingerprint\":   \"\\261D\",\n    ) {\n      .key-#{$name}::before {\n        padding-right: px2em(6.4px);\n        content: $code;\n      }\n    }\n\n    // Define keyboard keys with right icon\n    @each $name, $code in (\n        // Extra Keys\n    ) {\n      .key-#{$name}::after {\n        padding-left: px2em(6.4px);\n        content: $code;\n      }\n    }\n  }\n}\n", "// General styling for repository link icons\n:root > * {\n  --magiclink-email-icon: svg-load(\"@mdi/svg/svg/mail.svg\");\n  --magiclink-github-icon: svg-load(\"@mdi/svg/svg/github.svg\");\n  --magiclink-bitbucket-icon: svg-load(\"@mdi/svg/svg/bitbucket.svg\");\n  --magiclink-gitlab-icon: svg-load(\"@mdi/svg/svg/gitlab.svg\");\n  --magiclink-commit-icon: svg-load(\"@primer/octicons/build/svg/git-commit-24.svg\");\n  --magiclink-compare-icon: svg-load(\"@primer/octicons/build/svg/file-diff-24.svg\");\n  --magiclink-pull-icon: svg-load(\"@primer/octicons/build/svg/git-pull-request-24.svg\");\n  --magiclink-issue-icon: svg-load(\"@primer/octicons/build/svg/issue-opened-24.svg\");\n  --magiclink-discussion-icon: svg-load(\"@primer/octicons/build/svg/comment-discussion-24.svg\");\n}\n\n.md-typeset {\n  a[href^=\"mailto:\"]:not(.magiclink-ignore)::before {\n    mask-image: var(--magiclink-email-icon);\n  }\n\n  .magiclink-repository,\n  .magiclink-compare,\n  .magiclink-commit,\n  .magiclink-pull,\n  .magiclink-issue,\n  .magiclink-discussion,\n  a[href^=\"mailto:\"] {\n    &:not(.magiclink-ignore) {\n      position: relative;\n      padding-left: px2em(22px);\n\n      &::before {\n        position: absolute;\n        top: 0;\n        left: 0;\n        display: block;\n        box-sizing: border-box;\n        width: px2em(20px);\n        height: px2em(20px);\n        background-color: var(--md-typeset-a-color);\n        background-size: px2em(20px);\n        transition: background-color 125ms;\n        mask-repeat: no-repeat;\n        mask-size: contain;\n        content: \"\";\n      }\n\n      &:hover::before {\n        background-color: var(--md-accent-fg-color);\n      }\n    }\n  }\n}\n\n// Assign icons to repository links\n@each $name, $icon in (\n  \"commit\": \"commit\",\n  \"compare\": \"compare\",\n  \"pull\": \"pull\",\n  \"issue\": \"issue\",\n  \"discussion\": \"discussion\"\n) {\n  .md-typeset {\n    .magiclink-#{$name}:not(.magiclink-ignore) {\n      &::before {\n        mask-image: var(--magiclink-#{$icon}-icon);\n      }\n    }\n  }\n}\n\n// Assign icons to repository links\n@each $name, $icon in (\n  \"github\": \"github\",\n  \"gitlab\": \"gitlab\",\n  \"bitbucket\": \"bitbucket\"\n) {\n  .md-typeset {\n    .magiclink-repository.magiclink-#{$name}:not(.magiclink-ignore) {\n      &::before {\n        mask-image: var(--magiclink-#{$icon}-icon);\n      }\n    }\n  }\n}\n", "/* Shadow boxes sometimes give issues, so just pad. */\n.md-typeset mark:not(.critic) {\n  box-shadow: none;\n}\n", ".md-typeset {\n\n  /* Progress Bars */\n  .progress-label {\n    position: absolute;\n    width: 100%;\n    margin: 0;\n    color: var(--md-text-color);\n    font-weight: 700;\n    line-height: px2rem(28px);\n    white-space: nowrap;\n    text-align: center;\n    text-shadow:\n      px2em(-1px) px2em(-1px) px2em(6px) var(--md-default-bg-color--light),\n      px2em(1px) px2em(-1px) px2em(6px) var(--md-default-bg-color--light),\n      px2em(-1px) px2em(1px) px2em(6px) var(--md-default-bg-color--light),\n      px2em(1px) px2em(1px) px2em(6px) var(--md-default-bg-color--light);\n  }\n\n  .progress-bar {\n    float: left;\n    height: px2rem(24px);\n    background-color: $clr-blue-a400;\n  }\n\n  /* Stripe animation */\n  .candystripe-animate {\n    .progress-bar{\n      animation: animate-stripes 3s linear infinite;\n    }\n  }\n\n  .progress {\n    position: relative;\n    display: block;\n    width: 100%;\n    height: px2rem(24px);\n    margin: px2rem(10px) 0;\n    background-color: var(--md-default-fg-color--lightest);\n\n    &.thin {\n      height: px2rem(8px);\n      margin-top: px2rem(18px);\n\n      .progress-label {\n        margin-top: px2rem(-8px);\n      }\n\n      .progress-bar {\n        height: px2rem(8px);\n      }\n    }\n\n    &.candystripe .progress-bar {\n      background-image:\n        linear-gradient(\n          135deg,\n          var(--md-progress-stripe) 27%,\n          transparent 27%,\n          transparent 52%,\n          var(--md-progress-stripe) 52%,\n          var(--md-progress-stripe) 77%,\n          transparent 77%,\n          transparent\n        );\n      background-size: px2rem(40px) px2rem(40px);\n    }\n  }\n\n  @each $percent, $color in (\n    \"100\": var(--md-progress-100),\n    \"80\": var(--md-progress-80),\n    \"60\": var(--md-progress-60),\n    \"40\": var(--md-progress-40),\n    \"20\": var(--md-progress-20),\n    \"0\": var(--md-progress-0)\n  ) {\n    .progress-#{$percent}plus {\n      .progress-bar {\n        background-color: $color;\n      }\n    }\n  }\n\n  @keyframes animate-stripes {\n    0% {\n      background-position: 0 0;\n    }\n\n    100% {\n      background-position: px2rem(120px) 0;\n    }\n  }\n}\n", "/* Dark mode changes */\n[data-md-color-scheme=\"dracula\"] {\n  .md-typeset {\n    .tabbed-set > .tabbed-labels {\n      box-shadow: 0 px2rem(-1px) var(--md-default-fg-color--lighter) inset;\n    }\n  }\n}\n\n/* Style code blocks to fill full tab,\n   but otherwise, pad content. */\n.md-typeset {\n  .tabbed-alternate {\n    &.tabbed-set .tabbed-control {\n      width: px2rem(40px);\n\n      &[hidden] {\n        width: px2rem(24px);\n        opacity: 0;\n      }\n    }\n\n    &.tabbed-set > .tabbed-content > .tabbed-block {\n      padding: 0 px2rem(12px);\n\n      > {\n        pre,\n        .codehilite,\n        .codehilitetable,\n        .highlight,\n        .highlighttable {\n\n          &:only-child {\n\n            span.filename {\n              margin-top: 0;\n            }\n\n            margin-right: px2rem(-24px);\n            margin-left: px2rem(-24px);\n            padding-right: px2rem(12px);\n            padding-left: px2rem(12px);\n          }\n        }\n\n        .collapse-code:only-child {\n          margin-top: 0;\n\n          margin-right: px2rem(-24px);\n          margin-left: px2rem(-24px);\n          padding-right: px2rem(12px);\n          padding-left: px2rem(12px);\n\n          > .code-footer {\n            left: px2rem(12px)\n          }\n        }\n\n        diagram-div:only-child {\n          margin-right: px2rem(-24px);\n          margin-left: px2rem(-24px);\n          padding-right: px2rem(12px);\n          padding-left: px2rem(12px);\n        }\n      }\n    }\n  }\n}\n\n.js .md-typeset .tabbed-labels::before {\n  background-color: var(--md-accent-fg-color);\n}\n\n/* Ignore mobile overflow styling that extends the tab bar */\n@include break-to-device(mobile) {\n  [dir=ltr] .md-content__inner > .tabbed-set .tabbed-labels {\n    padding-left: 0;\n  }\n\n  .md-content__inner > .tabbed-set .tabbed-labels {\n    max-width: 100%;\n    margin: 0;\n    padding-inline-start: 0;\n    scroll-padding-inline-start: 0;\n\n    &::after {\n      padding-inline-end: 0;\n      content: none;\n    }\n\n    // Tabbed control previous\n    ~ .tabbed-control--prev {\n      margin-inline-start: 0;\n      padding-inline-start: 0;\n    }\n\n    // Tabbed control next\n    ~ .tabbed-control--next {\n      margin-inline-end: 0;\n      padding-inline-end: 0;\n    }\n  }\n}\n", "/* Dark mode changes */\n[data-md-color-scheme=\"dracula\"] {\n  .md-typeset table:not([class]) {\n    box-shadow: var(--md-shadow-z2);\n\n    tr:hover {\n      background-color: rgba(0,0,0,.08);\n    }\n\n    th {\n      color: var(--md-text-color);\n      background-color: var(--md-default-bg-color--ultra-dark);\n      border-bottom: px2rem(1px) solid var(--md-primary-fg-color);\n    }\n\n    td {\n      border-top: px2rem(1px) solid var(--md-default-fg-color--lighter);\n    }\n  }\n}\n", "/* Dark mode changes */\n[data-md-color-scheme=\"dracula\"] {\n  .md-typeset .task-list-control {\n    .task-list-indicator::before {\n    \tbackground-color: var(--md-default-fg-color--lighter);\n    }\n\n    [type=checkbox]:checked + .task-list-indicator::before {\n    \tbackground-color: $drac-green;\n    }\n  }\n}\n", "// Header anchors\n.md-typeset {\n  .headerlink {\n    width: px2em(16px);\n    height: px2em(16px);\n    vertical-align: middle;\n    background-color: var(--md-default-fg-color--lighter);\n    background-size: px2em(16px);\n    mask-size: px2em(16px);\n    mask-repeat: no-repeat;\n    visibility: visible;\n    mask-image: svg-load(\"@mdi/svg/svg/link.svg\");\n  }\n\n  .headerlink:hover,\n  [id]:target .headerlink {\n    background-color: var(--md-accent-fg-color);\n  }\n}\n", "/* Special handling of Mermaid content. */\ndiagram-div {\n  overflow: auto;\n}\n", "html {\n  background-color: transparent;\n}\n\n[data-md-component=\"announce\"] .twemoji {\n  color: var(--md-primary-fg-color);\n}\n\n/* Dark mode changes */\n[data-md-color-scheme=\"dracula\"] {\n\n  --md-text-color: var(--md-default-fg-color);\n  background-color: var(--md-default-bg-color);\n\n  // Footer color shades\n  --md-footer-bg-color:                transparent;\n  --md-footer-bg-color--dark:          var(--md-default-bg-color--darkest);\n\n  // Header colors\n  --md-header-fg-color: var(--md-text-color);\n  --md-header-bg-color: var(--md-default-bg-color--darkest);\n\n  // Header\n  .md-header {\n    color: var(--md-text-color);\n    background-color: var(--md-header-bg-color);\n    border-bottom: px2rem(1px) solid var(--md-primary-fg-color);\n\n    &[data-md-state=shadow] {\n        box-shadow: 0 0 px2rem(4px) rgba(0,0,0,.15),\n                    0 0 px2rem(4px) px2rem(8px) rgba(0,0,0,.2);\n    }\n  }\n\n  .md-top {\n    background-color: var(--md-default-bg-color--dark);\n\n    &:hover {\n      background-color: var(--md-primary-fg-color);\n    }\n  }\n\n  // Tabs\n  .md-tabs {\n    color: var(--md-text-color);\n    background-color: var(--md-primary-fg-color--transparent);\n\n    &__link--active {\n        color: var(--md-primary-fg-color);\n    }\n\n    &__link:hover {\n        color: var(--md-accent-fg-color);\n    }\n  }\n\n  // Hero\n  .md-hero {\n    color: var(--md-text-color);\n    background-color: var(--md-primary-fg-color--transparent);\n  }\n\n  // Navigation\n  .md-nav__source {\n    color: var(--md-text-color);\n  }\n\n  .md-nav__link[data-md-state=blur] {\n    color: var(--md-default-fg-color--light);\n  }\n\n  .md-nav__item .md-nav__link--active {\n    color: var(--md-primary-fg-color);\n  }\n\n  .md-nav__link {\n    &:focus,\n    &:hover {\n      color: var(--md-accent-fg-color);\n    }\n  }\n\n  @include break-to-device(tablet) {\n\n    .md-nav--primary .md-nav__item--active > .md-nav__link:not(:hover) {\n      color: var(--md-primary-fg-color);\n    }\n\n    // Site title in main navigation\n    .md-nav--primary .md-nav__title {\n      color: var(--md-text-color);\n      background-color: var(--md-header-bg-color);\n      border-bottom: px2rem(1px) solid var(--md-primary-fg-color);\n    }\n  }\n\n  @include break-to-device(tablet portrait) {\n\n    // Repository containing source\n    .md-nav__source {\n      color: var(--md-text-color);\n      background-color: var(--md-primary-fg-color--transparent);\n    }\n\n    .md-nav .md-nav__title {\n      color: var(--md-text-color);\n      background-color: var(--md-header-bg-color);\n      border-bottom: px2rem(1px) solid var(--md-primary-fg-color);\n    }\n  }\n\n  // Search\n  .md-search__input {\n    color: var(--md-text-color);\n    background-color: var(--md-accent-bg-color--light);\n\n    &:hover {\n      background-color: var(--md-default-bg-color--light);\n    }\n\n    ~ .md-search__icon {\n      color: var(--md-text-color);\n    }\n    &::placeholder {\n      color: var(--md-default-fg-color--light);\n    }\n  }\n\n  [data-md-toggle=search]:checked~.md-header .md-search__input {\n    background-color: transparent;\n  }\n\n  .md-search__suggest {\n    color: var(--md-default-autocomplete-fg-color);\n  }\n\n  .md-search__overlay, .md-overlay {\n    background-color: var(--md-default-bg-color--light);\n  }\n\n  // Footer\n  .md-footer {\n    &-nav__direction {\n      color: var(--md-primary-fg-color);\n    }\n\n    &-meta {\n      border-top: px2rem(1px) solid var(--md-primary-fg-color);\n    }\n  }\n\n  // Announcements\n  [data-md-component=\"announce\"] {\n    background-color: var(--md-default-bg-color--ultra-dark);\n  }\n}\n\n/* Don't force capitalization of `H5` elements. */\n.md-typeset {\n  h5 {\n    color: var(--md-text-color);\n    text-transform: none;\n  }\n}\n\n.md-typeset__scrollwrap,\n.md-sidebar__scrollwrap,\n.md-search__scrollwrap,\n.md-typeset pre > code,\n.md-typeset div.mermaid,\n.md-typeset div.diagram,\n.md-typeset mermaid-div,\n.md-typeset diagram-div,\n.md-typeset pre.arithmatex,\n.md-typeset div.arithmatex {\n  // Override native scrollbar styles\n  &::-webkit-scrollbar {\n    width: px2rem(4px);\n    height: px2rem(4px);\n  }\n\n  &::-webkit-scrollbar-corner {\n    background-color: transparent;\n  }\n\n  // Scrollbar thumb\n  &::-webkit-scrollbar-thumb {\n    background-color: var(--md-default-fg-color--lighter);\n\n    // Hovered scrollbar thumb\n    &:hover {\n      background-color: var(--md-accent-fg-color);\n    }\n  }\n\n  // Firefox scrollbar and thumb\n  scrollbar-color: var(--md-default-fg-color--lighter) transparent;\n  scrollbar-width: thin;\n\n  // Firefox hovered scrollbar and thumb\n  &:hover {\n    scrollbar-color: var(--md-accent-fg-color) transparent;\n  }\n}\n", "////\n/// Copyright (c) 2016-2020 <PERSON> <<EMAIL>>\n///\n/// Permission is hereby granted, free of charge, to any person obtaining a\n/// copy of this software and associated documentation files (the \"Software\"),\n/// to deal in the Software without restriction, including without limitation\n/// the rights to use, copy, modify, merge, publish, distribute, sublicense,\n/// and/or sell copies of the Software, and to permit persons to whom the\n/// Software is furnished to do so, subject to the following conditions:\n///\n/// The above copyright notice and this permission notice shall be included in\n/// all copies or substantial portions of the Software.\n///\n/// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n/// IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n/// FITNESS FOR A PARTICULAR PURPOSE AND NON-INFRINGEMENT. IN NO EVENT SHALL\n/// THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n/// LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\n/// FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER\n/// DEALINGS\n////\n\n// ----------------------------------------------------------------------------\n// Variables\n// ----------------------------------------------------------------------------\n\n///\n/// Device-specific breakpoints\n///\n/// @example\n///   $break-devices: (\n///     mobile: (\n///       portrait:  220px  479px,\n///       landscape: 480px  719px\n///     ),\n///     tablet: (\n///       portrait:  720px  959px,\n///       landscape: 960px  1219px\n///     ),\n///     screen: (\n///       small:     1220px 1599px,\n///       medium:    1600px 1999px,\n///       large:     2000px\n///     )\n///   );\n///\n$break-devices: () !default;\n\n// ----------------------------------------------------------------------------\n// Helpers\n// ----------------------------------------------------------------------------\n\n///\n/// Choose minimum and maximum device widths\n///\n@function break-select-min-max($devices) {\n  $min: 1000000;\n  $max: 0;\n  @each $key, $value in $devices {\n    @while type-of($value) == map {\n      $value: break-select-min-max($value);\n    }\n    @if type-of($value) == list {\n      @each $number in $value {\n        @if type-of($number) == number {\n          $min: min($number, $min);\n          @if $max != null {\n            $max: max($number, $max);\n          }\n        } @else {\n          @error \"Invalid number: #{$number}\";\n        }\n      }\n    } @else if type-of($value) == number {\n      $min: min($value, $min);\n      $max: null;\n    } @else {\n      @error \"Invalid value: #{$value}\";\n    }\n  }\n  @return $min, $max;\n}\n\n///\n/// Select minimum and maximum widths for a device breakpoint\n///\n@function break-select-device($device) {\n  $current: $break-devices;\n  @for $n from 1 through length($device) {\n    @if type-of($current) == map {\n      $current: map-get($current, nth($device, $n));\n    } @else {\n      @error \"Invalid device map: #{$devices}\";\n    }\n  }\n  @if type-of($current) == list or type-of($current) == number {\n    $current: (default: $current);\n  }\n  @return break-select-min-max($current);\n}\n\n// ----------------------------------------------------------------------------\n// Mixins\n// ----------------------------------------------------------------------------\n\n///\n/// A minimum-maximum media query breakpoint\n///\n@mixin break-at($breakpoint) {\n  @if type-of($breakpoint) == number {\n    @media screen and (min-width: $breakpoint) {\n      @content;\n    }\n  } @else if type-of($breakpoint) == list {\n    $min: nth($breakpoint, 1);\n    $max: nth($breakpoint, 2);\n    @if type-of($min) == number and type-of($max) == number {\n      @media screen and (min-width: $min) and (max-width: $max) {\n        @content;\n      }\n    } @else {\n      @error \"Invalid breakpoint: #{$breakpoint}\";\n    }\n  } @else {\n    @error \"Invalid breakpoint: #{$breakpoint}\";\n  }\n}\n\n///\n/// An orientation media query breakpoint\n///\n@mixin break-at-orientation($breakpoint) {\n  @if type-of($breakpoint) == string {\n    @media screen and (orientation: $breakpoint) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid breakpoint: #{$breakpoint}\";\n  }\n}\n\n///\n/// A maximum-aspect-ratio media query breakpoint\n///\n@mixin break-at-ratio($breakpoint) {\n  @if type-of($breakpoint) == number {\n    @media screen and (max-aspect-ratio: $breakpoint) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid breakpoint: #{$breakpoint}\";\n  }\n}\n\n///\n/// A minimum-maximum media query device breakpoint\n///\n@mixin break-at-device($device) {\n  @if type-of($device) == string {\n    $device: $device,;\n  }\n  @if type-of($device) == list {\n    $breakpoint: break-select-device($device);\n    @if nth($breakpoint, 2) != null {\n      $min: nth($breakpoint, 1);\n      $max: nth($breakpoint, 2);\n      @media screen and (min-width: $min) and (max-width: $max) {\n        @content;\n      }\n    } @else {\n      @error \"Invalid device: #{$device}\";\n    }\n  } @else {\n    @error \"Invalid device: #{$device}\";\n  }\n}\n\n///\n/// A minimum media query device breakpoint\n///\n@mixin break-from-device($device) {\n  @if type-of($device) == string {\n    $device: $device,;\n  }\n  @if type-of($device) == list {\n    $breakpoint: break-select-device($device);\n    $min: nth($breakpoint, 1);\n    @media screen and (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid device: #{$device}\";\n  }\n}\n\n///\n/// A maximum media query device breakpoint\n///\n@mixin break-to-device($device) {\n  @if type-of($device) == string {\n    $device: $device,;\n  }\n  @if type-of($device) == list {\n    $breakpoint: break-select-device($device);\n    $max: nth($breakpoint, 2);\n    @media screen and (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @error \"Invalid device: #{$device}\";\n  }\n}\n"]}