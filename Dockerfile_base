FROM feathertrainers.azurecr.io/expertswe-base:20250401

# Install uv
RUN pip install uv

# Create virtual environment with python 3.10
RUN uv venv --python 3.10 /.venv

# Clone repo and pin commit
WORKDIR /home
RUN git clone https://github.com/Cinnamon/kotaemon.git
RUN cd kotaemon && git checkout a3e2e20735c2e95682f9d56226451453cdad7e04

# Create virtual environment using uv and Python 3.10
WORKDIR /home/<USER>
RUN /.venv/bin/python -m ensurepip
RUN /.venv/bin/python -m pip install --upgrade pip
RUN /.venv/bin/pip install pre-commit

# Install the project (editable mode)
WORKDIR /home/<USER>/libs/kotaemon
RUN /.venv/bin/python -m pip install -U --upgrade-strategy eager -e .[all]

# Run tests once to pre-cache dependencies
RUN /.venv/bin/pre-commit clean && /.venv/bin/pre-commit run --all-files
RUN /.venv/bin/pytest

WORKDIR /home/<USER>