{"version": 3, "file": "material-extra-3rdparty-E-i8w1WA.js", "sources": ["material-extra-3rdparty.js"], "sourcesContent": ["// MathJax configuration\n\nif (!('mathjaxConfig' in window)) {\n  window.MathJax = {\n    tex: {\n      inlineMath: [[\"\\\\(\", \"\\\\)\"]],\n      displayMath: [[\"\\\\[\", \"\\\\]\"]],\n      processEscapes: true,\n      processEnvironments: true,\n      tagSide: \"right\",\n      tagIndent: \".8em\",\n      multlineWidth: \"85%\",\n      tags: \"ams\"\n    },\n    options: {\n      ignoreHtmlClass: \".*\",\n      processHtmlClass: \"arithmatex\"\n    }\n  }\n}\n\nif (!('mermaidConfig' in window)) {\n  // Our loader looks for `mermaidConfig` and will load the the appropriate\n  // configuration based on our current scheme: light, dark, etc.\n  window.mermaidConfig = {\n    dracula: {\n      startOnLoad: false,\n      theme: \"base\",\n      themeCSS: \"\\\n        * {\\\n          --drac-page-bg: hsl(233, 15%, 23%);\\\n          --drac-white-fg: hsl(60, 30%, 96%);\\\n          --drac-purple-fg: hsl(265, 89%, 78%);\\\n          --drac-purple-bg: hsl(265, 25%, 39%);\\\n          --drac-yellow-fg: hsl(65, 92%, 76%);\\\n          --drac-blue-fg: hsl(225, 27%, 51%);\\\n        }\\\n        \\\n        /* General */\\\n        [id^='_diagram'] {\\\n          background-color: var(--drac-page-bg);\\\n        }\\\n        \\\n        /* Entity Relationship */\\\n        rect.relationshipLabelBox {\\\n          opacity: 0.75 !important;\\\n          fill: var(--drac-purple-bg) !important;\\\n        }\\\n        defs marker#ZERO_OR_MORE_END circle {\\\n          fill: var(--drac-page-bg) !important;\\\n          stroke: var(--drac-purple-fg) !important;\\\n        }\\\n        defs marker#ZERO_OR_MORE_END path {\\\n          stroke: var(--drac-purple-fg) !important;\\\n        }\\\n        defs marker#ZERO_OR_MORE_START circle{\\\n          fill: var(--drac-page-bg) !important;\\\n          stroke: var(--drac-purple-fg) !important;\\\n        }\\\n        defs marker#ZERO_OR_MORE_START path {\\\n          stroke: var(--drac-purple-fg) !important;\\\n        }\\\n        defs marker#ONLY_ONE_START path {\\\n          stroke: var(--drac-purple-fg) !important;\\\n        }\\\n        defs marker#ONLY_ONE_END path {\\\n          stroke: var(--drac-purple-fg) !important;\\\n        }\\\n        defs marker#ZERO_OR_ONE_START path {\\\n          stroke: var(--drac-purple-fg) !important;\\\n        }\\\n        defs marker#ZERO_OR_ONE_END path {\\\n          stroke: var(--drac-purple-fg) !important;\\\n        }\\\n        defs marker#ONE_OR_MORE_START path {\\\n          stroke: var(--drac-purple-fg) !important;\\\n        }\\\n        defs marker#ONE_OR_MORE_END path {\\\n          stroke: var(--drac-purple-fg) !important;\\\n        }\\\n        \\\n        /* Flowchart */\\\n        .labelText,\\\n        :not(.branchLabel) > .label text {\\\n          fill: var(--drac-purple-fg);\\\n        }\\\n        .edgeLabel text {\\\n          fill: var(--drac-purple-fg) !important;\\\n        }\\\n        .edgeLabel rect {\\\n          opacity: 0.75 !important;\\\n          fill: var(--drac-purple-bg) !important;\\\n        }\\\n        \\\n        .grey rect.label-container { \\\n          fill: var(--drac-purple-bg) !important;\\\n          stroke: var(--drac-purple-fg) !important;\\\n        } \\\n        /* Sequence */\\\n        line[id^='actor'] {\\\n          stroke: var(--drac-blue-fg);\\\n        }\\\n        .noteText {\\\n          fill: var(--drac-yellow-fg);\\\n        }\\\n        \\\n        /* Gantt */\\\n        .sectionTitle {\\\n          fill: var(--drac-purple-fg) !important;\\\n        }\\\n        \\\n        .grid .tick line {\\\n          stroke: var(--drac-blue-fg) !important;\\\n        }\\\n        \\\n        .grid .tick text {\\\n          fill: var(--drac-purple-fg);\\\n        }\\\n        \\\n        /* Class Diagram */\\\n        .statediagram-state rect.divider {\\\n          fill: transparent !important;\\\n        }\\\n        \\\n        /* State Diagram */\\\n        .stateGroup circle[style$=\\\"fill: black;\\\"] {\\\n          fill: var(--drac-purple-bg) !important;\\\n          stroke: var(--drac-purple-bg) !important;\\\n        }\\\n        \\\n        .stateGroup circle[style$=\\\"fill: white;\\\"] {\\\n          fill: var(--drac-purple-bg) !important;\\\n          stroke: var(--drac-purple-fg) !important;\\\n        }\\\n        \\\n        .stateGroup .composit {\\\n          fill: var(--drac-page-bg);\\\n        }\\\n        /* Pie */\\\n        text.slice {\\\n          fill: var(--drac-white-fg) !important;\\\n        }\\\n        /* Git Graph */\\\n        .commit-bullets .commit-reverse,\\\n        .commit-bullets .commit-merge, \\\n        .commit-bullets .commit-highlight-inner {\\\n          fill: var(--drac-page-bg) !important;\\\n          stroke: var(--drac-page-bg) !important;\\\n        }\\\n        \",\n      themeVariables: {\n        darkMode: true,\n        background: \"#323443\",\n        mainBkg: \"#604b7d\",\n        textColor: \"#bf95f9\",\n        lineColor: \"#bf95f9\",\n        errorBkgColor: \"#802c2c\",\n        errorTextColor: \"#ff5757\",\n        primaryColor: \"#604b7d\",\n        primaryTextColor: \"#bf95f9\",\n        primaryBorderColor: \"#bf95f9\",\n        secondaryColor: \"#297d3e\",\n        secondaryTextColor: \"#52fa7c\",\n        secondaryBorderColor: \"#52fa7c\",\n        tertiaryColor: \"#303952\",\n        tertiaryTextColor: \"#6071a4\",\n        tertiaryBorderColor: \"#6071a4\",\n        noteBkgColor: \"#797d45\",\n        noteTextColor: \"#f1fa89\",\n        noteBorderColor: \"#f1fa89\",\n        edgeLabelBackground: \"#604b7d\",\n        edgeLabelText: \"#604b7d\",\n\n        actorLineColor: \"#6071a4\",\n\n        activeTaskBkgColor: \"#803d63\",\n        activeTaskBorderColor: \"#ff7ac6\",\n        doneTaskBkgColor: \"#297d3e\",\n        doneTaskBorderColor: \"#52fa7c\",\n        critBkgColor: \"#802c2c\",\n        critBorderColor: \"#ff5757\",\n        taskTextColor: \"#bf95f9\",\n        taskTextOutsideColor: \"#bf95f9\",\n        taskTextLightColor: \"#bf95f9\",\n        sectionBkgColor: \"#bf95f9b3\",\n        sectionBkgColor2: \"#bf95f966\",\n        altSectionBkgColor: \"#323443\",\n        todayLineColor: \"#ff7ac6\",\n        gridColor: \"#6071a4\",\n        defaultLinkColor: \"#8be8fd\",\n\n        altBackground: \"#bf95f9\",\n\n        classText: \"#bf95f9\",\n\n        fillType0: \"#406080\",\n        fillType1: \"#46747f\",\n        fillType2: \"#297d3e\",\n        fillType3: \"#805c36\",\n        fillType4: \"#803d63\",\n        fillType5: \"#604b7d\",\n        fillType6: \"#802c2c\",\n        fillType7: \"#797d45\",\n        fillType8: \"#7c7c79\",\n\n        git0: \"#ff5555\",\n        git1: \"#ffb86c\",\n        git2: \"#f1fa8c\",\n        git3: \"#50fa7b\",\n        git4: \"#8be9fd\",\n        git5: \"#809fff\",\n        git6: \"#ff79c6\",\n        git7: \"#bd93f9\",\n\n        gitInv0: \"#ff5555\",\n        gitInv1: \"#ffb86c\",\n        gitInv2: \"#f1fa8c\",\n        gitInv3: \"#50fa7b\",\n        gitInv4: \"#8be9fd\",\n        gitInv5: \"#809fff\",\n        gitInv6: \"#ff79c6\",\n        gitInv7: \"#bd93f9\",\n\n        gitBranchLabel0: \"#323443\",\n        gitBranchLabel1: \"#323443\",\n        gitBranchLabel2: \"#323443\",\n        gitBranchLabel3: \"#323443\",\n        gitBranchLabel4: \"#323443\",\n        gitBranchLabel5: \"#323443\",\n        gitBranchLabel6: \"#323443\",\n        gitBranchLabel7: \"#323443\",\n\n        commitLabelColor: '#52fa7c',\n        commitLabelBackground: '#297d3e'\n      },\n      flowchart: {\n        htmlLabels: false,\n        useMaxWidth: false\n      },\n      er: {\n        useMaxWidth: false\n      },\n      sequence: {\n        useMaxWidth: false,\n        // Mermaid handles Firefox a little different.\n        // For some reason, it doesn't attach font sizes to the labels in Firefox.\n        // If we specify the documented defaults, font sizes are written to the labels in Firefox.\n        noteFontWeight: \"14px\",\n        actorFontSize: \"14px\",\n        messageFontSize: \"16px\"\n      },\n      journey: {\n        useMaxWidth: false\n      },\n      pie: {\n        useMaxWidth: false\n      },\n      gantt: {\n        useMaxWidth: false\n      },\n      gitGraph: {\n        useMaxWidth: false\n      }\n    },\n\n    default: {\n      startOnLoad: false,\n      theme: \"default\",\n      flowchart: {\n        htmlLabels: false,\n        useMaxWidth: false\n      },\n      er: {\n        useMaxWidth: false\n      },\n      sequence: {\n        useMaxWidth: false,\n        noteFontWeight: \"14px\",\n        actorFontSize: \"14px\",\n        messageFontSize: \"16px\"\n      },\n      journey: {\n        useMaxWidth: false\n      },\n      pie: {\n        useMaxWidth: false\n      },\n      gantt: {\n        useMaxWidth: false\n      },\n      gitGraph: {\n        useMaxWidth: false\n      }\n    },\n\n    slate: {\n      startOnLoad: false,\n      theme: \"dark\",\n      flowchart: {\n        htmlLabels: false,\n        useMaxWidth: false\n      },\n      er: {\n        useMaxWidth: false\n      },\n      sequence: {\n        useMaxWidth: false,\n        noteFontWeight: \"14px\",\n        actorFontSize: \"14px\",\n        messageFontSize: \"16px\"\n      },\n      journey: {\n        useMaxWidth: false\n      },\n      pie: {\n        useMaxWidth: false\n      },\n      gantt: {\n        useMaxWidth: false\n      },\n      gitGraph: {\n        useMaxWidth: false\n      }\n    }\n  }\n}\n"], "names": ["window", "MathJax", "tex", "inlineMath", "displayMath", "processEscapes", "processEnvironments", "tagSide", "tagIndent", "m<PERSON><PERSON><PERSON><PERSON><PERSON>", "tags", "options", "ignoreHtmlClass", "processHtmlClass", "mermaidConfig", "dracula", "startOnLoad", "theme", "themeCSS", "themeVariables", "darkMode", "background", "mainBkg", "textColor", "lineColor", "errorBkgColor", "errorTextColor", "primaryColor", "primaryTextColor", "primaryBorderColor", "secondaryColor", "secondaryTextColor", "secondaryBorderColor", "tertiaryColor", "tertiaryTextColor", "tertiaryBorderColor", "noteBkgColor", "noteTextColor", "noteBorderColor", "edgeLabelBackground", "edgeLabelText", "actor<PERSON><PERSON><PERSON><PERSON><PERSON>", "activeTaskBkgColor", "activeTaskBorderColor", "doneTaskBkgColor", "doneTaskBorderColor", "critBkgColor", "critBorderColor", "taskTextColor", "taskTextOutsideColor", "taskTextLightColor", "sectionBkgColor", "sectionBkgColor2", "altSectionBkgColor", "todayLineColor", "gridColor", "defaultLinkColor", "altBackground", "classText", "fillType0", "fillType1", "fillType2", "fillType3", "fillType4", "fillType5", "fillType6", "fillType7", "fillType8", "git0", "git1", "git2", "git3", "git4", "git5", "git6", "git7", "gitInv0", "gitInv1", "gitInv2", "gitInv3", "gitInv4", "gitInv5", "gitInv6", "gitInv7", "gitBranchLabel0", "gitBranchLabel1", "gitBranchLabel2", "gitBranchLabel3", "gitBranchLabel4", "gitBranchLabel5", "gitBranchLabel6", "gitBranchLabel7", "commitLabelColor", "commitLabelBackground", "flowchart", "htmlLabels", "useMaxWidth", "er", "sequence", "noteFontWeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "messageFontSize", "journey", "pie", "gantt", "gitGraph", "default", "slate"], "mappings": "yBAEM,kBAAmBA,SACvBA,OAAOC,QAAU,CACfC,IAAK,CACHC,WAAY,CAAC,CAAC,MAAO,QACrBC,YAAa,CAAC,CAAC,MAAO,QACtBC,gBAAgB,EAChBC,qBAAqB,EACrBC,QAAS,QACTC,UAAW,OACXC,cAAe,MACfC,KAAM,OAERC,QAAS,CACPC,gBAAiB,KACjBC,iBAAkB,gBAKlB,kBAAmBb,SAGvBA,OAAOc,cAAgB,CACrBC,QAAS,CACPC,aAAa,EACbC,MAAO,OACPC,SAAU,w/GA0HVC,eAAgB,CACdC,UAAU,EACVC,WAAY,UACZC,QAAS,UACTC,UAAW,UACXC,UAAW,UACXC,cAAe,UACfC,eAAgB,UAChBC,aAAc,UACdC,iBAAkB,UAClBC,mBAAoB,UACpBC,eAAgB,UAChBC,mBAAoB,UACpBC,qBAAsB,UACtBC,cAAe,UACfC,kBAAmB,UACnBC,oBAAqB,UACrBC,aAAc,UACdC,cAAe,UACfC,gBAAiB,UACjBC,oBAAqB,UACrBC,cAAe,UAEfC,eAAgB,UAEhBC,mBAAoB,UACpBC,sBAAuB,UACvBC,iBAAkB,UAClBC,oBAAqB,UACrBC,aAAc,UACdC,gBAAiB,UACjBC,cAAe,UACfC,qBAAsB,UACtBC,mBAAoB,UACpBC,gBAAiB,YACjBC,iBAAkB,YAClBC,mBAAoB,UACpBC,eAAgB,UAChBC,UAAW,UACXC,iBAAkB,UAElBC,cAAe,UAEfC,UAAW,UAEXC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,UAAW,UACXC,UAAW,UAEXC,KAAM,UACNC,KAAM,UACNC,KAAM,UACNC,KAAM,UACNC,KAAM,UACNC,KAAM,UACNC,KAAM,UACNC,KAAM,UAENC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UACTC,QAAS,UAETC,gBAAiB,UACjBC,gBAAiB,UACjBC,gBAAiB,UACjBC,gBAAiB,UACjBC,gBAAiB,UACjBC,gBAAiB,UACjBC,gBAAiB,UACjBC,gBAAiB,UAEjBC,iBAAkB,UAClBC,sBAAuB,WAEzBC,UAAW,CACTC,YAAY,EACZC,aAAa,GAEfC,GAAI,CACFD,aAAa,GAEfE,SAAU,CACRF,aAAa,EAIbG,eAAgB,OAChBC,cAAe,OACfC,gBAAiB,QAEnBC,QAAS,CACPN,aAAa,GAEfO,IAAK,CACHP,aAAa,GAEfQ,MAAO,CACLR,aAAa,GAEfS,SAAU,CACRT,aAAa,IAIjBU,QAAS,CACP1F,aAAa,EACbC,MAAO,UACP6E,UAAW,CACTC,YAAY,EACZC,aAAa,GAEfC,GAAI,CACFD,aAAa,GAEfE,SAAU,CACRF,aAAa,EACbG,eAAgB,OAChBC,cAAe,OACfC,gBAAiB,QAEnBC,QAAS,CACPN,aAAa,GAEfO,IAAK,CACHP,aAAa,GAEfQ,MAAO,CACLR,aAAa,GAEfS,SAAU,CACRT,aAAa,IAIjBW,MAAO,CACL3F,aAAa,EACbC,MAAO,OACP6E,UAAW,CACTC,YAAY,EACZC,aAAa,GAEfC,GAAI,CACFD,aAAa,GAEfE,SAAU,CACRF,aAAa,EACbG,eAAgB,OAChBC,cAAe,OACfC,gBAAiB,QAEnBC,QAAS,CACPN,aAAa,GAEfO,IAAK,CACHP,aAAa,GAEfQ,MAAO,CACLR,aAAa,GAEfS,SAAU,CACRT,aAAa"}