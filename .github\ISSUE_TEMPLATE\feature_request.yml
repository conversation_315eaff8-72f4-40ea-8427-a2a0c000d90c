name: "Feature Request"
description: Brainstorm and propose new features for the project
title: "[REQUEST] "
labels: ["enhancement"]
body:
  - type: markdown
    attributes:
      value: |
        *Please fill this form with as much information as possible.*
  - type: textarea
    id: reference_issues
    attributes:
      label: "Reference Issues"
      description: Common issues
      placeholder: "#Issues IDs"
    validations:
      required: false
  - type: textarea
    id: summary
    attributes:
      label: "Summary"
      description: Provide a brief explanation of the feature
      placeholder: Describe in a few lines your feature request
    validations:
      required: true
  - type: textarea
    id: basic_example
    attributes:
      label: "Basic Example"
      description: Indicate here some basic examples of your feature.
      placeholder: A few specific words about your feature request.
    validations:
      required: true
  - type: textarea
    id: drawbacks
    attributes:
      label: "Drawbacks"
      description: What are the drawbacks/impacts of your feature request ?
      placeholder: Identify the drawbacks and impacts while being neutral on your feature request
    validations:
      required: true
  - type: textarea
    id: additional_information
    attributes:
      label: "Additional information"
      description: Add any additional information that you think is important for your feature request
      placeholder:
    validations:
      required: false
