# Getting Started with <PERSON><PERSON>

![type:video](https://github.com/Cinnamon/kotaemon/assets/25688648/815ecf68-3a02-4914-a0dd-3f8ec7e75cd9)

This page is intended for **end users** who want to use the `kotaemon` tool for Question
Answering on local documents. If you are a **developer** who wants contribute to the project, please visit the [development](development/index.md) page.

## Installation (Online HuggingFace Space) - easy (10 mins)

Visit this [guide](online_install.md).

## Installation (Offline) - intermediate (20 mins)

### Download

Download the `kotaemon-app.zip` file from the [latest release](https://github.com/Cinnamon/kotaemon/releases/latest/).

### Run setup script

0. Unzip the downloaded file.
1. Navigate to the `scripts` folder and start an installer that matches your OS:
   - Windows: `run_windows.bat`. Just double click the file.
   - macOS: `run_macos.sh`
     1. Right click on your file and select Open with and Other.
     2. Enable All Applications and choose Terminal.
     3. NOTE: If you always want to open that file with <PERSON>, then check Always Open With.
     4. From now on, double click on your file and it should work.
   - Linux: `run_linux.sh`. Please run the script using `bash run_linux.sh` in your terminal.
2. After the installation, the installer will ask to launch the ktem's UI, answer to continue.
3. If launched, the application will be open automatically in your browser.
4. Default login information is: `username: admin / password: admin`. You should change this credential right after the first login on the UI.

## Launch

To launch the app after initial setup or any change, simply run the `run_*` script again.

A browser window will be opened and greets you with this screen:

![Chat tab](https://raw.githubusercontent.com/Cinnamon/kotaemon/main/docs/images/chat-tab.png)

## Usage

For how to use the application, see [Usage](usage.md). This page will also be available to
you within the application.

## Feedback

Feel free to create a bug report or a feature request on our [repo](https://github.com/Cinnamon/kotaemon/issues).
