name: "Lint PR"

on:
  pull_request:
    types:
      - opened
      - edited
      - synchronize

permissions:
  pull-requests: write

jobs:
  pr-title:
    name: Validate PR title
    runs-on: ubuntu-latest
    permissions: write-all
    steps:
      - uses: amannn/action-semantic-pull-request@v5
        id: lint_pr_title
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - uses: marocchino/sticky-pull-request-comment@v2
        # When the previous steps fails, the workflow would stop. By adding this
        # condition you can continue the execution with the populated error message.
        if: always() && (steps.lint_pr_title.outputs.error_message != null)
        with:
          header: pr-title-lint-error
          message: |
            Hey there and thank you for opening this pull request! 👋🏼

            We require pull request titles to follow the [Conventional Commits specification](https://www.conventionalcommits.org/en/v1.0.0/) and it looks like your proposed title needs to be adjusted.
            Details:
            ```
            ${{ steps.lint_pr_title.outputs.error_message }}
            ```

      # Delete a previous comment when the issue has been resolved
      - if: ${{ steps.lint_pr_title.outputs.error_message == null }}
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: pr-title-lint-error
          delete: true

  commitlint:
    if: false # Disable this job for now
    name: Validate commit messages
    runs-on: ubuntu-latest
    permissions: write-all
    steps:
      - uses: actions/checkout@v4
      - uses: wagoid/commitlint-github-action@v6
        id: commitlint
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          configFile: ./.commitlintrc
      - uses: buildingcash/json-to-markdown-table-action@v1
        if: always() && (steps.commitlint.outcome != 'success')
        id: table
        with:
          json: ${{ steps.commitlint.outputs.results }}
      - uses: marocchino/sticky-pull-request-comment@v2
        if: always() && (steps.commitlint.outcome != 'success')
        with:
          header: commitlint-error
          message: |
            **All commits** in this PR need to follow the [Conventional Commits specification](https://www.conventionalcommits.org/en/v1.0.0/) and [.commitlintrc](${{ github.server_url }}/${{ github.repository }}/blob/${{ github.head_ref || github.ref_name }}/.commitlintrc).
            Details:
            ${{ steps.table.outputs.table }}

      - if: ${{ steps.commitlint.outcome == 'success' }}
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: commitlint-error
          delete: true
