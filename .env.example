# this is an example .env file, use it to create your own .env file and place it in the root of the project

# settings for OpenAI
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_KEY=<YOUR_OPENAI_KEY>
OPENAI_CHAT_MODEL=gpt-4o-mini
OPENAI_EMBEDDINGS_MODEL=text-embedding-3-large

# settings for Azure OpenAI
AZURE_OPENAI_ENDPOINT=
AZURE_OPENAI_API_KEY=
OPENAI_API_VERSION=2024-02-15-preview
AZURE_OPENAI_CHAT_DEPLOYMENT=gpt-35-turbo
AZURE_OPENAI_EMBEDDINGS_DEPLOYMENT=text-embedding-ada-002

# settings for Cohere
COHERE_API_KEY=<COHERE_API_KEY>

# settings for Mistral
# MISTRAL_API_KEY=placeholder

# settings for VoyageAI
VOYAGE_API_KEY=<VOYAGE_API_KEY>

# settings for local models
LOCAL_MODEL=qwen2.5:7b
LOCAL_MODEL_EMBEDDINGS=nomic-embed-text

# settings for GraphRAG
GRAPHRAG_API_KEY=<YOUR_OPENAI_KEY>
GRAPHRAG_LLM_MODEL=gpt-4o-mini
GRAPHRAG_EMBEDDING_MODEL=text-embedding-3-small

# set to true if you want to use customized GraphRAG config file
USE_CUSTOMIZED_GRAPHRAG_SETTING=false

# settings for Azure DI
AZURE_DI_ENDPOINT=
AZURE_DI_CREDENTIAL=

# settings for Adobe API
# get free credential at https://acrobatservices.adobe.com/dc-integration-creation-app-cdn/main.html?api=pdf-extract-api
# also install pip install "pdfservices-sdk@git+https://github.com/niallcm/pdfservices-python-sdk.git@bump-and-unfreeze-requirements"
PDF_SERVICES_CLIENT_ID=
PDF_SERVICES_CLIENT_SECRET=

# settings for PDF.js
PDFJS_VERSION_DIST="pdfjs-4.0.379-dist"

# variable for authentication method selection
# for authentication with google leave empty
# for authentication with keycloak :
# AUTHENTICATION_METHOD="KEYCLOAK"

AUTHENTICATION_METHOD=

# settings for keycloak
KEYCLOAK_SERVER_URL=
KEYCLOAK_CLIENT_ID=
KEYCLOAK_REALM=
KEYCLOAK_CLIENT_SECRET=
