
{% import "partials/language.html" as lang with context %}
<footer class="md-footer">
  {% if page.previous_page or page.next_page %}
    <nav
      class="md-footer__inner md-grid"
      aria-label="{{ lang.t('footer.title') }}"
    >
      {% if page.previous_page %}
        <a
          href="{{ page.previous_page.url | url }}"
          class="md-footer__link md-footer__link--prev"
          rel="prev"
        >
          <div class="md-footer__button md-icon">
            {% include ".icons/material/arrow-left.svg" %}
          </div>
          <div class="md-footer__title">
            <div class="md-ellipsis">
              <span class="md-footer__direction">
                {{ lang.t("footer.previous") }}
              </span>
              {{ page.previous_page.title }}
            </div>
          </div>
        </a>
      {% endif %}
      {% if page.next_page %}
        <a
          href="{{ page.next_page.url | url }}"
          class="md-footer__link md-footer__link--next"
          rel="next"
        >
          <div class="md-footer__title">
            <div class="md-ellipsis">
              <span class="md-footer__direction">
                {{ lang.t("footer.next") }}
              </span>
              {{ page.next_page.title }}
            </div>
          </div>
          <div class="md-footer__button md-icon">
            {% include ".icons/material/arrow-right.svg" %}
          </div>
        </a>
      {% endif %}
    </nav>
  {% endif %}
</footer>
