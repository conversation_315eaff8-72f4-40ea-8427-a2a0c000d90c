repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.3.0
    hooks:
      - id: check-yaml
      - id: check-toml
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: detect-aws-credentials
        args: ["--allow-missing-credentials"]
      - id: detect-private-key
      - id: check-added-large-files
  - repo: https://github.com/ambv/black
    rev: 22.3.0
    hooks:
      - id: black
        language_version: python3
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ["--profile", "black"]
        language_version: python3.10
  - repo: https://github.com/pycqa/flake8
    rev: 4.0.1
    hooks:
      - id: flake8
        args: ["--max-line-length", "88", "--extend-ignore", "E203"]
  - repo: https://github.com/myint/autoflake
    rev: v1.4
    hooks:
      - id: autoflake
        args:
          [
            "--in-place",
            "--remove-unused-variables",
            "--remove-all-unused-imports",
            "--ignore-init-module-imports",
            "--exclude=tests/*",
          ]
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v2.7.1
    hooks:
      - id: prettier
        types_or: [markdown, yaml]
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: "v1.5.1"
    hooks:
      - id: mypy
        additional_dependencies: [types-PyYAML==6.0.12.11, "types-requests"]
        args: ["--check-untyped-defs", "--ignore-missing-imports"]
