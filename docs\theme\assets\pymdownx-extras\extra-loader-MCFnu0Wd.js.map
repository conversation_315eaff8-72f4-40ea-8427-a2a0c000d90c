{"version": 3, "file": "extra-loader-MCFnu0Wd.js", "sources": ["uml.js", "extra-loader.js", "arithmatex.js"], "sourcesContent": ["/* Notes (as of Mermaid 8.7.0):\n * - Gantt: width is always relative to the parent, if you have a small parent, the chart will be squashed.\n *   Can't help it.\n * - Journey: Suffers from the same issues that Gantt does.\n * - Pie: These charts have no default height or width. Good luck pinning them down to a reasonable size.\n * - Git: The render portion is agnostic to the size of the parent element. But padding of the SVG is relative\n *   to the parent element. You will never find a happy size.\n */\n\n/**\n * Targets special code or div blocks and converts them to UML.\n * @param {string} className is the name of the class to target.\n * @return {void}\n */\nexport default async className => {\n\n  // Custom element to encapsulate Mermaid content.\n  class MermaidDiv extends HTMLElement {\n\n    /**\n    * Creates a special Mermaid div shadow DOM.\n    * Works around issues of shared IDs.\n    * @return {void}\n    */\n    constructor() {\n      super()\n\n      // Create the Shadow DOM and attach style\n      const shadow = this.attachShadow({mode: \"open\"})\n      const style = document.createElement(\"style\")\n      style.textContent = `\n      :host {\n        display: block;\n        line-height: initial;\n        font-size: 16px;\n      }\n      div.diagram {\n        margin: 0;\n        overflow: visible;\n      }`\n      shadow.appendChild(style)\n    }\n  }\n\n  if (typeof customElements.get(\"diagram-div\") === \"undefined\") {\n    customElements.define(\"diagram-div\", MermaidDiv)\n  }\n\n  const getFromCode = parent => {\n    // Handles <pre><code> text extraction.\n    let text = \"\"\n    for (let j = 0; j < parent.childNodes.length; j++) {\n      const subEl = parent.childNodes[j]\n      if (subEl.tagName.toLowerCase() === \"code\") {\n        for (let k = 0; k < subEl.childNodes.length; k++) {\n          const child = subEl.childNodes[k]\n          const whitespace = /^\\s*$/\n          if (child.nodeName === \"#text\" && !(whitespace.test(child.nodeValue))) {\n            text = child.nodeValue\n            break\n          }\n        }\n      }\n    }\n    return text\n  }\n\n  // We use this to determine if we want the dark or light theme.\n  // This is specific for our MkDocs Material environment.\n  // You should load your configs based on your own environment's needs.\n  const defaultConfig = {\n    startOnLoad: false,\n    theme: \"default\",\n    flowchart: {\n      htmlLabels: false\n    },\n    er: {\n      useMaxWidth: false\n    },\n    sequence: {\n      useMaxWidth: false,\n      noteFontWeight: \"14px\",\n      actorFontSize: \"14px\",\n      messageFontSize: \"16px\"\n    }\n  }\n  mermaid.mermaidAPI.globalReset()\n  // Non Material themes should just use \"default\"\n  let scheme = null\n  try {\n    scheme = document.querySelector(\"[data-md-color-scheme]\").getAttribute(\"data-md-color-scheme\")\n  } catch (err) {\n    scheme = \"default\"\n  }\n  const config = (typeof mermaidConfig === \"undefined\") ?\n    defaultConfig :\n    mermaidConfig[scheme] || (mermaidConfig.default || defaultConfig)\n  mermaid.initialize(config)\n\n  // Find all of our Mermaid sources and render them.\n  const blocks = document.querySelectorAll(`pre.${className}, diagram-div`)\n  const surrogate = document.querySelector(\"html body\")\n  for (let i = 0; i < blocks.length; i++) {\n    const block = blocks[i]\n    const parentEl = (block.tagName.toLowerCase() === \"diagram-div\") ?\n      block.shadowRoot.querySelector(`pre.${className}`) :\n      block\n\n    // Create a temporary element with the typeset and size we desire.\n    // Insert it at the end of our parent to render the SVG.\n    const temp = document.createElement(\"div\")\n    temp.style.visibility = \"hidden\"\n    temp.style.display = \"display\"\n    temp.style.padding = \"0\"\n    temp.style.margin = \"0\"\n    temp.style.lineHeight = \"initial\"\n    temp.style.fontSize = \"16px\"\n    surrogate.appendChild(temp)\n\n    try {\n      const res = await mermaid.render(`_diagram_${i}`, getFromCode(parentEl), temp)\n      const content = res.svg\n      const fn = res.bindFunctions\n      const el = document.createElement(\"div\")\n      el.className = className\n      el.innerHTML = content\n      if (fn) {\n        fn(el)\n      }\n\n      // Insert the render where we want it and remove the original text source.\n      // Mermaid will clean up the temporary element.\n      const shadow = document.createElement(\"diagram-div\")\n      shadow.shadowRoot.appendChild(el)\n      block.parentNode.insertBefore(shadow, block)\n      parentEl.style.display = \"none\"\n      shadow.shadowRoot.appendChild(parentEl)\n      if (parentEl !== block) {\n        block.parentNode.removeChild(block)\n      }\n    } catch (err) {} // eslint-disable-line no-empty\n\n    if (surrogate.contains(temp)) {\n      surrogate.removeChild(temp)\n    }\n  }\n}\n", "import uml from \"./uml\"\nimport arithmatex from \"./arithmatex\"\n\n// Main function\n(() => {\n  let umlPromise = Promise.resolve()\n  let mathPromise = Promise.resolve()\n\n  const observer = new MutationObserver(mutations => {\n    mutations.forEach(mutation => {\n      if (mutation.type === \"attributes\") {\n        let scheme = mutation.target.getAttribute(\"data-md-color-scheme\")\n        if (!scheme) {\n          scheme = \"default\"\n        }\n        localStorage.setItem(\"data-md-color-scheme\", scheme)\n        if (typeof mermaid !== \"undefined\") {\n          uml(\"diagram\")\n        }\n      }\n    })\n  })\n\n  const main = () => {\n    observer.observe(document.querySelector(\"body\"), {attributeFilter: [\"data-md-color-scheme\"]})\n\n    if (typeof mermaid !== \"undefined\") {\n      umlPromise = umlPromise.then(() => {\n        uml(\"diagram\")\n      }).catch(err => {\n        console.log(`UML loading failed...${err}`) // eslint-disable-line no-console\n      })\n    }\n\n    if (typeof katex !== \"undefined\") {\n      mathPromise = mathPromise.then(() => {\n        arithmatex(\"arithmatex\", \"katex\")\n      }).catch(err => {\n        console.log(`Math loading failed...${err}`) // eslint-disable-line no-console\n      })\n    } else if (typeof MathJax !== \"undefined\" && 'typesetPromise' in MathJax) {\n      mathPromise = mathPromise.then(() => {\n        arithmatex(\"arithmatex\", \"mathjax\")\n      }).catch(err => {\n        console.log(`Math loading failed...${err}`) // eslint-disable-line no-console\n      })\n    }\n  }\n\n  if (window.document$) {\n    // Material specific hook\n    window.document$.subscribe(main)\n  } else {\n    // Normal non-Material specific hook\n    document.addEventListener(\"DOMContentLoaded\", main)\n  }\n})()\n", "export default (className, mode) => {\n  if (mode === 'katex') {\n    const maths = document.querySelectorAll(`.${className}`)\n\n    for (let i = 0; i < maths.length; i++) {\n      const tex = maths[i].textContent || maths[i].innerText\n\n      if (tex.startsWith('\\\\(') && tex.endsWith('\\\\)')) {\n        katex.render(tex.slice(2, -2), maths[i], {'displayMode': false})\n      } else if (tex.startsWith('\\\\[') && tex.endsWith('\\\\]')) {\n        katex.render(tex.slice(2, -2), maths[i], {'displayMode': true})\n      }\n    }\n  } else if (mode === 'mathjax') {\n    MathJax.startup.output.clearCache()\n    MathJax.typesetClear()\n    MathJax.texReset()\n    MathJax.typesetPromise()\n  }\n}\n"], "names": ["umlPromise", "mathPromise", "observer", "main", "uml", "_ref", "_regeneratorRuntime", "mark", "_callee", "className", "MermaidDiv", "getFromCode", "defaultConfig", "scheme", "config", "blocks", "surrogate", "i", "block", "parentEl", "temp", "res", "content", "fn", "el", "shadow", "wrap", "_context", "prev", "next", "_HTMLElement", "_inherits", "_super", "_this", "_classCallCheck", "call", "this", "attachShadow", "mode", "style", "document", "createElement", "textContent", "append<PERSON><PERSON><PERSON>", "_wrapNativeSuper", "HTMLElement", "customElements", "get", "define", "parent", "text", "j", "childNodes", "length", "subEl", "tagName", "toLowerCase", "k", "child", "nodeName", "test", "nodeValue", "startOnLoad", "theme", "flowchart", "htmlLabels", "er", "useMaxWidth", "sequence", "noteFontWeight", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "messageFontSize", "mermaid", "mermaidAPI", "globalReset", "querySelector", "getAttribute", "err", "mermaidConfig", "initialize", "querySelectorAll", "concat", "shadowRoot", "visibility", "display", "padding", "margin", "lineHeight", "fontSize", "render", "sent", "svg", "bindFunctions", "innerHTML", "parentNode", "insertBefore", "<PERSON><PERSON><PERSON><PERSON>", "t0", "contains", "stop", "_x", "apply", "arguments", "arithmatex", "maths", "tex", "innerText", "startsWith", "endsWith", "katex", "slice", "displayMode", "MathJax", "startup", "output", "clearCache", "typesetClear", "texReset", "typesetPromise", "Promise", "resolve", "MutationObserver", "mutations", "for<PERSON>ach", "mutation", "type", "target", "localStorage", "setItem", "observe", "attributeFilter", "then", "console", "log", "window", "document$", "subscribe", "addEventListener"], "mappings": "woSAcA,ICTMA,EACAC,EAEEC,EAeAC,EDTRC,EAAA,WAAA,MAAAC,KAAAC,IAAAC,MAAe,SAAAC,EAAMC,GAAS,IAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAAC,EAAA,OAAAnB,IAAAoB,MAAA,SAAAC,GAAA,cAAAA,EAAAC,KAAAD,EAAAE,MAAA,KAAA,EAGtBnB,WAAUoB,GAAAC,EAAArB,EAAAoB,GAAA,cAAAE,KAAAtB,qJAOd,SAAAA,IAAc,IAAAuB,EAAAC,OAAAxB,GAIZ,IAAMe,GAHNQ,EAAAD,EAAAG,KAAAC,OAGoBC,aAAa,CAACC,KAAM,SAClCC,EAAQC,SAASC,cAAc,SAWZ,OAVzBF,EAAMG,YASJ,2LACFjB,EAAOkB,YAAYJ,GAAMN,CAC3B,CAAC,SAAAvB,sFAAAkC,EAxBsBC,mBA2BwB,IAAtCC,eAAeC,IAAI,gBAC5BD,eAAeE,OAAO,cAAetC,GAGjCC,EAAc,SAAAsC,GAGlB,IADA,IAAIC,EAAO,GACFC,EAAI,EAAGA,EAAIF,EAAOG,WAAWC,OAAQF,IAAK,CACjD,IAAMG,EAAQL,EAAOG,WAAWD,GAChC,GAAoC,SAAhCG,EAAMC,QAAQC,cAChB,IAAK,IAAIC,EAAI,EAAGA,EAAIH,EAAMF,WAAWC,OAAQI,IAAK,CAChD,IAAMC,EAAQJ,EAAMF,WAAWK,GAE/B,GAAuB,UAAnBC,EAAMC,WADS,QAC4BC,KAAKF,EAAMG,WAAa,CACrEX,EAAOQ,EAAMG,UACb,KACF,CACF,CAEJ,CACA,OAAOX,CACT,EAKMtC,EAAgB,CACpBkD,aAAa,EACbC,MAAO,UACPC,UAAW,CACTC,YAAY,GAEdC,GAAI,CACFC,aAAa,GAEfC,SAAU,CACRD,aAAa,EACbE,eAAgB,OAChBC,cAAe,OACfC,gBAAiB,SAGrBC,QAAQC,WAAWC,cAEf7D,EAAS,KACb,IACEA,EAAS2B,SAASmC,cAAc,0BAA0BC,aAAa,uBACxE,CAAC,MAAOC,GACPhE,EAAS,SACX,CACMC,EAAmC,oBAAlBgE,cACrBlE,EACAkE,cAAcjE,IAAYiE,cAAa,SAAYlE,EACrD4D,QAAQO,WAAWjE,GAGbC,EAASyB,SAASwC,wBAAgBC,OAAQxE,EAAS,kBACnDO,EAAYwB,SAASmC,cAAc,aAChC1D,EAAI,EAAC,KAAA,GAAA,KAAEA,EAAIF,EAAOsC,QAAM,CAAA1B,EAAAE,KAAA,GAAA,KAAA,CAeJ,OAdrBX,EAAQH,EAAOE,GACfE,EAA4C,gBAAhCD,EAAMqC,QAAQC,cAC9BtC,EAAMgE,WAAWP,cAAa,OAAAM,OAAQxE,IACtCS,GAIIE,EAAOoB,SAASC,cAAc,QAC/BF,MAAM4C,WAAa,SACxB/D,EAAKmB,MAAM6C,QAAU,UACrBhE,EAAKmB,MAAM8C,QAAU,IACrBjE,EAAKmB,MAAM+C,OAAS,IACpBlE,EAAKmB,MAAMgD,WAAa,UACxBnE,EAAKmB,MAAMiD,SAAW,OACtBxE,EAAU2B,YAAYvB,GAAKO,EAAAC,KAAA,GAAAD,EAAAE,KAAA,GAGP2C,QAAQiB,OAAM,YAAAR,OAAahE,GAAKN,EAAYQ,GAAWC,GAAK,KAAA,GAAxEC,EAAGM,EAAA+D,KACHpE,EAAUD,EAAIsE,IACdpE,EAAKF,EAAIuE,eACTpE,EAAKgB,SAASC,cAAc,QAC/BhC,UAAYA,EACfe,EAAGqE,UAAYvE,EACXC,GACFA,EAAGC,IAKCC,EAASe,SAASC,cAAc,gBAC/ByC,WAAWvC,YAAYnB,GAC9BN,EAAM4E,WAAWC,aAAatE,EAAQP,GACtCC,EAASoB,MAAM6C,QAAU,OACzB3D,EAAOyD,WAAWvC,YAAYxB,GAC1BA,IAAaD,GACfA,EAAM4E,WAAWE,YAAY9E,GAC9BS,EAAAE,KAAA,GAAA,MAAA,KAAA,GAAAF,EAAAC,KAAA,GAAAD,EAAAsE,GAAAtE,EAAA,MAAA,IAAA,KAAA,GAGCX,EAAUkF,SAAS9E,IACrBJ,EAAUgF,YAAY5E,GACvB,KAAA,GA1CgCH,IAAGU,EAAAE,KAAA,GAAA,MAAA,KAAA,GAAA,IAAA,MAAA,OAAAF,EAAAwE,OAAA,GAAA3F,EAAA,KAAA,CAAA,CAAA,GAAA,KA4CvC,mLAAA,OAAA,SAAA4F,GAAA,OAAA/F,EAAAgG,MAAAjE,KAAAkE,UAAA,CAAA,CApID,GEdAC,EAAe,SAAC9F,EAAW6B,GACzB,GAAa,UAATA,EAGF,IAFA,IAAMkE,EAAQhE,SAASwC,qBAAgBC,OAAKxE,IAEnCQ,EAAI,EAAGA,EAAIuF,EAAMnD,OAAQpC,IAAK,CACrC,IAAMwF,EAAMD,EAAMvF,GAAGyB,aAAe8D,EAAMvF,GAAGyF,UAEzCD,EAAIE,WAAW,QAAUF,EAAIG,SAAS,OACxCC,MAAMpB,OAAOgB,EAAIK,MAAM,GAAI,GAAIN,EAAMvF,GAAI,CAAC8F,aAAe,IAChDN,EAAIE,WAAW,QAAUF,EAAIG,SAAS,QAC/CC,MAAMpB,OAAOgB,EAAIK,MAAM,GAAI,GAAIN,EAAMvF,GAAI,CAAC8F,aAAe,GAE7D,KACkB,YAATzE,IACT0E,QAAQC,QAAQC,OAAOC,aACvBH,QAAQI,eACRJ,QAAQK,WACRL,QAAQM,iBAEZ,EDdMtH,EAAauH,QAAQC,UACrBvH,EAAcsH,QAAQC,UAEpBtH,EAAW,IAAIuH,kBAAiB,SAAAC,GACpCA,EAAUC,SAAQ,SAAAC,GAChB,GAAsB,eAAlBA,EAASC,KAAuB,CAClC,IAAIhH,EAAS+G,EAASE,OAAOlD,aAAa,wBACrC/D,IACHA,EAAS,WAEXkH,aAAaC,QAAQ,uBAAwBnH,GACtB,oBAAZ2D,SACTpE,EAAI,UAER,CACF,GACF,IAEMD,EAAO,WACXD,EAAS+H,QAAQzF,SAASmC,cAAc,QAAS,CAACuD,gBAAiB,CAAC,0BAE7C,oBAAZ1D,UACTxE,EAAaA,EAAWmI,MAAK,WAC3B/H,EAAI,UACN,IAAE,OAAO,SAAAyE,GACPuD,QAAQC,IAAGpD,wBAAAA,OAAyBJ,GACtC,KAGmB,oBAAVgC,MACT5G,EAAcA,EAAYkI,MAAK,WAC7B5B,EAAW,aAAc,QAC3B,IAAE,OAAO,SAAA1B,GACPuD,QAAQC,IAAGpD,yBAAAA,OAA0BJ,GACvC,IAC4B,oBAAZmC,SAA2B,mBAAoBA,UAC/D/G,EAAcA,EAAYkI,MAAK,WAC7B5B,EAAW,aAAc,UAC3B,IAAE,OAAO,SAAA1B,GACPuD,QAAQC,IAAGpD,yBAAAA,OAA0BJ,GACvC,MAIAyD,OAAOC,UAETD,OAAOC,UAAUC,UAAUrI,GAG3BqC,SAASiG,iBAAiB,mBAAoBtI"}