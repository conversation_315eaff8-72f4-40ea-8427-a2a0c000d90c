## Feature Ideas with Moderate Difficulty

- **Feedback-Informed Retrieval Tuning**: Create a retrieval system that updates its ranking based on simple user feedback input, like thumbs up/down, to improve results gradually.

- **Collaborative Reasoning Agents**: Build a framework where multiple reasoning agents can work together by sharing intermediate answers to tackle complex queries.

- **Basic Multi-Modal Support**: Extend document QA to include simple image and table understanding, enabling questions about figures alongside text.

- **User-Friendly Pipeline Inspector UI**: Add a basic UI to visualize key steps in the retrieval and generation pipeline to help users understand how answers are formed.
## Chat

The kotaemon focuses on question and answering over a corpus of data. Below
is the gentle introduction about the chat functionality.

- Users can upload corpus of files.
- Users can converse to the chatbot to ask questions about the corpus of files.
- Users can view the reference in the files.
