# build backand and build dependencies
[build-system]
requires = ["setuptools >= 61.0", "wheel", "setuptools-git-versioning>=2.0,<3"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
include-package-data = false
packages.find.include = ["kotaemon*"]
packages.find.exclude = ["tests*", "env*"]

[tool.setuptools-git-versioning]
enabled = true
dev_template = "{tag}"
dirty_template = "{tag}"
tag_filter = "v?\\d+(\\.\\d+)*.*"

# metadata and dependencies
[project]
name = "kotaemon"
dynamic = ["version"]
requires-python = ">= 3.10"
description = "Kotaemon core library for AI development."
dependencies = [
    "azure-ai-documentintelligence",
    "beautifulsoup4>=4.12.3,<4.13",
    "click>=8.1.7,<9",
    "cohere>=5.3.2,<6",
    "cookiecutter>=2.6.0,<2.7",
    "fast_langdetect",
    "fastapi<=0.112.1",
    "gradio>=4.31.0,<4.40",
    "html2text==2024.2.26",
    "langchain>=0.1.16,<0.2.16",
    "langchain-community>=0.0.34,<=0.2.11",
    "langchain-openai>=0.1.4,<0.2.0",
    "langchain-google-genai>=1.0.3,<2.0.0",
    "langchain-anthropic",
    "langchain-ollama",
    "langchain-mistralai",
    "langchain-cohere>=0.2.4,<0.3.0",
    "llama-hub>=0.0.79,<0.1.0",
    "llama-index>=0.10.40,<0.11.0",
    "chromadb<=0.5.16",
    "llama-index-vector-stores-chroma>=0.1.9",
    "llama-index-vector-stores-lancedb",
    "openai>=1.23.6,<2",
    "matplotlib",
    "matplotlib-inline",
    "openpyxl>=3.1.2,<3.2",
    "opentelemetry-exporter-otlp-proto-grpc>=1.25.0", # https://github.com/chroma-core/chroma/issues/2571
    "pandas>=2.2.2,<2.3",
    "plotly<6.0.0",
    "PyMuPDF>=1.23,<=1.24.11",
    "pypdf>=4.2.0,<4.3",
    "pylance",
    "python-decouple", # for theflow
    "python-docx>=1.1.0,<1.2",
    "python-dotenv>=1.0.1,<1.1",
    "tenacity>=8.2.3,<8.3",
    "theflow>=0.8.6,<0.9.0",
    "trogon>=0.5.0,<0.6",
    "umap-learn==0.5.5",
    "tavily-python>=0.4.0",
    "pydantic<=2.10.6",
]
readme = "README.md"
authors = [
    { name = "@trducng", email = "<EMAIL>" },
    { name = "@lone17", email = "<EMAIL>" },
    { name = "@taprosoft", email = "<EMAIL>" },
    { name = "@cin-albert", email = "<EMAIL>" },
]
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]

[project.optional-dependencies]
adv = [
    "duckduckgo-search>=6.1.0,<6.2",
    "elasticsearch>=8.13.0,<8.14",
    "fastembed",
    "onnxruntime<v1.20",
    "googlesearch-python>=1.2.4,<1.3",
    "llama-cpp-python<0.2.8",
    "llama-index>=0.10.40,<0.11.0",
    "llama-index-vector-stores-milvus",
    "llama-index-vector-stores-qdrant",
    "sentence-transformers",
    "tabulate",
    "unstructured>=0.15.8,<0.16",
    "wikipedia>=1.4.0,<1.5",
    "voyageai>=0.3.0",
]
dev = [
    "black",
    "coverage",
    "flake8",
    "ipython",
    "pre-commit",
    "pytest",
    "pytest-mock",
    "sphinx",
]
all = ["kotaemon[adv,dev]"]

[project.scripts]
kotaemon = "kotaemon.cli:main"
