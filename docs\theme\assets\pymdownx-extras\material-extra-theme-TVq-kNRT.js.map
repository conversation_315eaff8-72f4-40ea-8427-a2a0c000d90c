{"version": 3, "file": "material-extra-theme-TVq-kNRT.js", "sources": ["material-extra-theme.js"], "sourcesContent": ["(() => {\n\n  const preferToggle = e => {\n    if (localStorage.getItem(\"data-md-prefers-color-scheme\") === \"true\") {\n      document.querySelector(\"body\").setAttribute(\"data-md-color-scheme\", (e.matches) ? \"dracula\" : \"default\")\n    }\n  }\n\n  const setupTheme = body => {\n    const preferSupported = window.matchMedia(\"(prefers-color-scheme)\").media !== \"not all\"\n    let scheme = localStorage.getItem(\"data-md-color-scheme\")\n    let prefers = localStorage.getItem(\"data-md-prefers-color-scheme\")\n\n    if (!scheme) {\n      scheme = \"dracula\"\n    }\n    if (!prefers) {\n      prefers = \"false\"\n    }\n\n    if (prefers === \"true\" && preferSupported) {\n      scheme = (window.matchMedia(\"(prefers-color-scheme: dark)\").matches) ? \"dracula\" : \"default\"\n    } else {\n      prefers = \"false\"\n    }\n\n    body.setAttribute(\"data-md-prefers-color-scheme\", prefers)\n    body.setAttribute(\"data-md-color-scheme\", scheme)\n\n    if (preferSupported) {\n      const matchListener = window.matchMedia(\"(prefers-color-scheme: dark)\")\n      matchListener.addListener(preferToggle)\n    }\n  }\n\n  const observer = new MutationObserver(mutations => {\n    mutations.forEach(mutation => {\n      if (mutation.type === \"childList\") {\n        if (mutation.addedNodes.length) {\n          for (let i = 0; i < mutation.addedNodes.length; i++) {\n            const el = mutation.addedNodes[i]\n\n            if (el.nodeType === 1 && el.tagName.toLowerCase() === \"body\") {\n              setupTheme(el)\n              break\n            }\n          }\n        }\n      }\n    })\n  })\n\n  observer.observe(document.querySelector(\"html\"), {childList: true})\n})()\n\nwindow.toggleScheme = () => {\n  const body = document.querySelector(\"body\")\n  const preferSupported = window.matchMedia(\"(prefers-color-scheme)\").media !== \"not all\"\n  let scheme = body.getAttribute(\"data-md-color-scheme\")\n  let prefer = body.getAttribute(\"data-md-prefers-color-scheme\")\n\n  if (preferSupported && scheme === \"default\" && prefer !== \"true\") {\n    prefer = \"true\"\n    scheme = (window.matchMedia(\"(prefers-color-scheme: dark)\").matches) ? \"dracula\" : \"default\"\n  } else if (preferSupported && prefer === \"true\") {\n    prefer = \"false\"\n    scheme = \"dracula\"\n  } else if (scheme === \"dracula\") {\n    prefer = \"false\"\n    scheme = \"default\"\n  } else {\n    prefer = \"false\"\n    scheme = \"dracula\"\n  }\n  localStorage.setItem(\"data-md-prefers-color-scheme\", prefer)\n  body.setAttribute(\"data-md-prefers-color-scheme\", prefer)\n  body.setAttribute(\"data-md-color-scheme\", scheme)\n}\n"], "names": ["preferToggle", "e", "localStorage", "getItem", "document", "querySelector", "setAttribute", "matches", "MutationObserver", "mutations", "for<PERSON>ach", "mutation", "type", "addedNodes", "length", "i", "el", "nodeType", "tagName", "toLowerCase", "body", "preferSupported", "scheme", "prefers", "window", "matchMedia", "media", "addListener", "observe", "childList", "toggleScheme", "getAttribute", "prefer", "setItem"], "mappings": "yBAAA,IAEQA,IAAe,SAAAC,GAC0C,SAAzDC,aAAaC,QAAQ,iCACvBC,SAASC,cAAc,QAAQC,aAAa,uBAAyBL,EAAEM,QAAW,UAAY,YA+BjF,IAAIC,kBAAiB,SAAAC,GACpCA,EAAUC,SAAQ,SAAAC,GAChB,GAAsB,cAAlBA,EAASC,MACPD,EAASE,WAAWC,OACtB,IAAK,IAAIC,EAAI,EAAGA,EAAIJ,EAASE,WAAWC,OAAQC,IAAK,CACnD,IAAMC,EAAKL,EAASE,WAAWE,GAE/B,GAAoB,IAAhBC,EAAGC,UAA+C,SAA7BD,EAAGE,QAAQC,cAA0B,CAlCrDC,EAmCIJ,EAlCfK,SACFC,SACAC,SAFEF,EAAwE,YAAtDG,OAAOC,WAAW,0BAA0BC,MAChEJ,EAASpB,aAAaC,QAAQ,wBAC9BoB,EAAUrB,aAAaC,QAAQ,gCAE9BmB,IACHA,EAAS,WAENC,IACHA,EAAU,SAGI,SAAZA,GAAsBF,EACxBC,EAAUE,OAAOC,WAAW,gCAAgClB,QAAW,UAAY,UAEnFgB,EAAU,QAGZH,EAAKd,aAAa,+BAAgCiB,GAClDH,EAAKd,aAAa,uBAAwBgB,GAEtCD,GACoBG,OAAOC,WAAW,gCAC1BE,YAAY3B,GAalB,KACF,CACF,CAtCW,IAAAoB,EACXC,EACFC,EACAC,CAsCJ,GACF,IAESK,QAAQxB,SAASC,cAAc,QAAS,CAACwB,WAAW,IAG/DL,OAAOM,aAAe,WACpB,IAAMV,EAAOhB,SAASC,cAAc,QAC9BgB,EAAwE,YAAtDG,OAAOC,WAAW,0BAA0BC,MAChEJ,EAASF,EAAKW,aAAa,wBAC3BC,EAASZ,EAAKW,aAAa,gCAE3BV,GAA8B,YAAXC,GAAmC,SAAXU,GAC7CA,EAAS,OACTV,EAAUE,OAAOC,WAAW,gCAAgClB,QAAW,UAAY,WAC1Ec,GAA8B,SAAXW,GAC5BA,EAAS,QACTV,EAAS,WACW,YAAXA,GACTU,EAAS,QACTV,EAAS,YAETU,EAAS,QACTV,EAAS,WAEXpB,aAAa+B,QAAQ,+BAAgCD,GACrDZ,EAAKd,aAAa,+BAAgC0B,GAClDZ,EAAKd,aAAa,uBAAwBgB,EAC5C"}