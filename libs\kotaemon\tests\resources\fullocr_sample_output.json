[{"csv_string": ",,,単位,実施例1,実施例2,実施例3,比較例1,比較例2,比較例3,比較例4\n熱硬化性樹脂組成物,熱硬化性樹脂,エポキシ樹脂1,質量%,10.2,12.4,12.4,10.2,12.4,,10.2\n,,エポキシ樹脂2,,-,-,-,-,-,14.4,-\n,硬化剤,硬化剤1,,4.8,6.6,6.6,4.8,6.6,7.6,4.8\n,無機充填材,無機充填剤1,,74,70,70,74,70,67,74\n,,無機充填剤2,,10,10,10,10,10,10,10\n,硬化促進剤,硬化促進剤1,,0.2,0.2,0.2,0.2,0.2,0.2,0.2\n,カップリング剤,カップリング剤1,,0.2,0.2,0.2,0.2,0.2,0.2,0.2\n,離型剤,離型剤1,,0.2,0.2,0.2,0.2,0.2,0.2,0.2\n,着色剤,着色剤1,,0.4,0.4,0.4,0.4,0.4,0.4,0.4\n,合計,,,100,100,100,100,100,100,100\n,,,単位,実施例1,実施例2,実施例3,比較例1,比較例2,比較例3,比較例4\n熱硬化性樹脂組成物,熱硬化性樹脂,エポキシ樹脂1,%,,10,10,(セラミック),(セラミック),,\n,,エポキシ樹脂2,,5,-,-,,,5,5\n,,エポキシ樹脂3,,5,-,-,,,5,5\n,,シアネート樹脂1,,10,10,10,,,10,10\n,,フェノール系硬化剤1,,5,5,5,,,5,5\n,硬化触媒,硬化触媒1,,0.2,0.2,0.2,,,0.2,0.2\n,無機充填材,無機充填材1,,74.8,74.8,74.8,,,74.8,74.8\n,合計,,,100,100,100,,,100,100\nIL1TD-L1TU,,,ppm,510,470,470,,,510,510\nL125D-L125U,,,ppm,390,330,330,,,390,390\nガラス転移温度 (Tg),,,°C,,224,224,,,235,235\n", "image": "7810d908b0ff4ce381dcab873196d133.jpg", "image_shape": [1653, 2339], "json": {"ocr": [{"location": [[237, 290], [352, 290], [352, 309], [237, 309]], "type": "textline", "text": "熱硬化性樹脂", "confidence_by_character": [0.9686674475669861, 0.9664099812507629, 0.9206223487854004, 0.9281898140907288, 0.962303102016449, 0.9605254530906677], "confidence_by_field": 0.9206223487854004, "original_text": "熱硬化性樹脂"}, {"location": [[694, 272], [739, 272], [739, 297], [694, 297]], "type": "textline", "text": "10.2", "confidence_by_character": [0.9169260263442993, 0.9255782961845398, 0.924151599407196, 0.9199540019035339], "confidence_by_field": 0.9169260263442993, "original_text": "10.2"}, {"location": [[767, 242], [843, 242], [843, 264], [767, 264]], "type": "textline", "text": "実施例2", "confidence_by_character": [0.9473932385444641, 0.9163241982460022, 0.7033942937850952, 0.8979774117469788], "confidence_by_field": 0.7033942937850952, "original_text": "実施例2"}, {"location": [[783, 272], [830, 272], [830, 297], [783, 297]], "type": "textline", "text": "12.4", "confidence_by_character": [0.9123356342315674, 0.9198176264762878, 0.9250789880752563, 0.92127925157547], "confidence_by_field": 0.9123356342315674, "original_text": "12.4"}, {"location": [[961, 268], [1011, 268], [1011, 298], [961, 298]], "type": "textline", "text": "10.2", "confidence_by_character": [0.917376697063446, 0.9228717684745789, 0.9257153868675232, 0.9199367761611938], "confidence_by_field": 0.917376697063446, "original_text": "10.2"}, {"location": [[1050, 268], [1100, 268], [1100, 298], [1050, 298]], "type": "textline", "text": "12.4", "confidence_by_character": [0.9123420119285583, 0.9206566214561462, 0.9267528057098389, 0.9224498867988586], "confidence_by_field": 0.9123420119285583, "original_text": "12.4"}, {"location": [[1130, 242], [1203, 242], [1203, 264], [1130, 264]], "type": "textline", "text": "比較例3", "confidence_by_character": [0.9575085639953613, 0.9481230974197388, 0.756417453289032, 0.9103184342384338], "confidence_by_field": 0.756417453289032, "original_text": "比較例3"}, {"location": [[236, 335], [297, 335], [297, 358], [236, 358]], "type": "textline", "text": "硬化剤", "confidence_by_character": [0.9772048592567444, 0.9326395988464355, 0.9597039222717285], "confidence_by_field": 0.9326395988464355, "original_text": "硬化剤"}, {"location": [[383, 335], [454, 335], [454, 358], [383, 358]], "type": "textline", "text": "硬化剤1", "confidence_by_character": [0.9785639047622681, 0.9359536170959473, 0.9465298056602478, 0.911139965057373], "confidence_by_field": 0.911139965057373, "original_text": "硬化剤1"}, {"location": [[385, 305], [510, 305], [510, 325], [385, 325]], "type": "textline", "text": "エポキシ樹脂2", "confidence_by_character": [0.9240000247955322, 0.9118557572364807, 0.8709841966629028, 0.898737370967865, 0.9585683345794678, 0.9542934894561768, 0.9125517010688782], "confidence_by_field": 0.8709841966629028, "original_text": "エポキシ樹脂2"}, {"location": [[385, 274], [509, 274], [509, 294], [385, 294]], "type": "textline", "text": "エポキシ樹脂1", "confidence_by_character": [0.9230291247367859, 0.913360595703125, 0.8715535402297974, 0.9017773270606995, 0.9544979333877563, 0.9605312943458557, 0.9045484662055969], "confidence_by_field": 0.8715535402297974, "original_text": "エポキシ樹脂1"}, {"location": [[601, 240], [644, 240], [644, 265], [601, 265]], "type": "textline", "text": "単位", "confidence_by_character": [0.9253715872764587, 0.9197276830673218], "confidence_by_field": 0.9197276830673218, "original_text": "単位"}, {"location": [[675, 242], [752, 242], [752, 264], [675, 264]], "type": "textline", "text": "実施例1", "confidence_by_character": [0.9467881917953491, 0.9383226037025452, 0.7930335402488708, 0.7423544526100159], "confidence_by_field": 0.7423544526100159, "original_text": "実施例1"}, {"location": [[856, 242], [933, 242], [933, 264], [856, 264]], "type": "textline", "text": "実施例3", "confidence_by_character": [0.9493170976638794, 0.9393699169158936, 0.7815940976142883, 0.8886772990226746], "confidence_by_field": 0.7815940976142883, "original_text": "実施例3"}, {"location": [[1232, 272], [1280, 272], [1280, 297], [1232, 297]], "type": "textline", "text": "10.2", "confidence_by_character": [0.9169086217880249, 0.9247543811798096, 0.9242917895317078, 0.9190678596496582], "confidence_by_field": 0.9169086217880249, "original_text": "10.2"}, {"location": [[709, 308], [722, 308], [722, 324], [709, 324]], "type": "textline", "text": "-", "confidence_by_character": [0.8763231635093689], "confidence_by_field": 0.8763231635093689, "original_text": "-"}, {"location": [[788, 334], [825, 334], [825, 359], [788, 359]], "type": "textline", "text": "6.6", "confidence_by_character": [0.9220069050788879, 0.9281726479530334, 0.9160889387130737], "confidence_by_field": 0.9160889387130737, "original_text": "6.6"}, {"location": [[799, 308], [812, 308], [812, 324], [799, 324]], "type": "textline", "text": "-", "confidence_by_character": [0.888385534286499], "confidence_by_field": 0.888385534286499, "original_text": "-"}, {"location": [[873, 273], [919, 273], [919, 297], [873, 297]], "type": "textline", "text": "12.4", "confidence_by_character": [0.912204921245575, 0.9198381900787354, 0.9228084087371826, 0.9204709529876709], "confidence_by_field": 0.912204921245575, "original_text": "12.4"}, {"location": [[949, 242], [1022, 242], [1022, 264], [949, 264]], "type": "textline", "text": "比較例1", "confidence_by_character": [0.9624030590057373, 0.9464587569236755, 0.7610214948654175, 0.9083214998245239], "confidence_by_field": 0.7610214948654175, "original_text": "比較例1"}, {"location": [[1040, 242], [1114, 242], [1114, 264], [1040, 264]], "type": "textline", "text": "比較例2", "confidence_by_character": [0.9574549794197083, 0.95237135887146, 0.7252858281135559, 0.9158275127410889], "confidence_by_field": 0.7252858281135559, "original_text": "比較例2"}, {"location": [[1219, 242], [1293, 242], [1293, 264], [1219, 264]], "type": "textline", "text": "比較例4", "confidence_by_character": [0.9668106436729431, 0.9518352746963501, 0.792457640171051, 0.91578209400177], "confidence_by_field": 0.792457640171051, "original_text": "比較例4"}, {"location": [[886, 307], [904, 307], [904, 324], [886, 324]], "type": "textline", "text": "-", "confidence_by_character": [0.8876243233680725], "confidence_by_field": 0.8876243233680725, "original_text": "-"}, {"location": [[980, 310], [993, 310], [993, 324], [980, 324]], "type": "textline", "text": "-", "confidence_by_character": [0.8970376253128052], "confidence_by_field": 0.8970376253128052, "original_text": "-"}, {"location": [[1065, 306], [1087, 306], [1087, 328], [1065, 328]], "type": "textline", "text": "-", "confidence_by_character": [0.8992383480072021], "confidence_by_field": 0.8992383480072021, "original_text": "-"}, {"location": [[1249, 310], [1262, 310], [1262, 324], [1249, 324]], "type": "textline", "text": "-", "confidence_by_character": [0.8871487379074097], "confidence_by_field": 0.8871487379074097, "original_text": "-"}, {"location": [[237, 382], [333, 382], [333, 401], [237, 401]], "type": "textline", "text": "無機充填材", "confidence_by_character": [0.952167809009552, 0.9192871451377869, 0.9678506255149841, 0.9592251181602478, 0.9547764658927917], "confidence_by_field": 0.9192871451377869, "original_text": "無機充填材"}, {"location": [[384, 367], [490, 367], [490, 386], [384, 386]], "type": "textline", "text": "無機充填剤1", "confidence_by_character": [0.9493359923362732, 0.9333418011665344, 0.9685581922531128, 0.9741933941841125, 0.9563856720924377, 0.9097828269004822], "confidence_by_field": 0.9097828269004822, "original_text": "無機充填剤1"}, {"location": [[697, 334], [734, 334], [734, 358], [697, 358]], "type": "textline", "text": "4.8", "confidence_by_character": [0.9166890978813171, 0.9267215132713318, 0.9219436645507812], "confidence_by_field": 0.9166890978813171, "original_text": "4.8"}, {"location": [[880, 337], [914, 337], [914, 359], [880, 359]], "type": "textline", "text": "6.6", "confidence_by_character": [0.9212806820869446, 0.9280492067337036, 0.917919933795929], "confidence_by_field": 0.917919933795929, "original_text": "6.6"}, {"location": [[701, 396], [733, 396], [733, 421], [701, 421]], "type": "textline", "text": "10", "confidence_by_character": [0.9160857200622559, 0.9220792651176453], "confidence_by_field": 0.9160857200622559, "original_text": "10"}, {"location": [[701, 364], [733, 364], [733, 389], [701, 389]], "type": "textline", "text": "74", "confidence_by_character": [0.9179115295410156, 0.9187763333320618], "confidence_by_field": 0.9179115295410156, "original_text": "74"}, {"location": [[793, 365], [820, 365], [820, 388], [793, 388]], "type": "textline", "text": "70", "confidence_by_character": [0.9186102151870728, 0.9202508330345154], "confidence_by_field": 0.9186102151870728, "original_text": "70"}, {"location": [[883, 367], [910, 367], [910, 388], [883, 388]], "type": "textline", "text": "70", "confidence_by_character": [0.9183272123336792, 0.920913577079773], "confidence_by_field": 0.9183272123336792, "original_text": "70"}, {"location": [[969, 334], [1006, 334], [1006, 359], [969, 359]], "type": "textline", "text": "4.8", "confidence_by_character": [0.9184820652008057, 0.9289669990539551, 0.9193996787071228], "confidence_by_field": 0.9184820652008057, "original_text": "4.8"}, {"location": [[970, 364], [1002, 364], [1002, 389], [970, 389]], "type": "textline", "text": "74", "confidence_by_character": [0.9170815348625183, 0.9173583984375], "confidence_by_field": 0.9170815348625183, "original_text": "74"}, {"location": [[1059, 334], [1094, 334], [1094, 359], [1059, 359]], "type": "textline", "text": "6.6", "confidence_by_character": [0.9224190711975098, 0.9298495650291443, 0.9167705178260803], "confidence_by_field": 0.9167705178260803, "original_text": "6.6"}, {"location": [[1143, 303], [1190, 303], [1190, 327], [1143, 327]], "type": "textline", "text": "14.4", "confidence_by_character": [0.9120434522628784, 0.9212027788162231, 0.9268625974655151, 0.9202296137809753], "confidence_by_field": 0.9120434522628784, "original_text": "14.4"}, {"location": [[1061, 364], [1093, 364], [1093, 391], [1061, 391]], "type": "textline", "text": "70", "confidence_by_character": [0.9193744659423828, 0.9198716878890991], "confidence_by_field": 0.9193744659423828, "original_text": "70"}, {"location": [[1148, 334], [1185, 334], [1185, 359], [1148, 359]], "type": "textline", "text": "7.6", "confidence_by_character": [0.9237031936645508, 0.922902524471283, 0.9160147905349731], "confidence_by_field": 0.9160147905349731, "original_text": "7.6"}, {"location": [[1149, 364], [1182, 364], [1182, 389], [1149, 389]], "type": "textline", "text": "67", "confidence_by_character": [0.9192152619361877, 0.9226703643798828], "confidence_by_field": 0.9192152619361877, "original_text": "67"}, {"location": [[1238, 334], [1275, 334], [1275, 358], [1238, 358]], "type": "textline", "text": "4.8", "confidence_by_character": [0.9171125292778015, 0.9256289005279541, 0.9216449856758118], "confidence_by_field": 0.9171125292778015, "original_text": "4.8"}, {"location": [[1243, 365], [1270, 365], [1270, 388], [1243, 388]], "type": "textline", "text": "74", "confidence_by_character": [0.9173100590705872, 0.9199501872062683], "confidence_by_field": 0.9173100590705872, "original_text": "74"}, {"location": [[384, 429], [495, 429], [495, 448], [384, 448]], "type": "textline", "text": "硬化促進剤1", "confidence_by_character": [0.9833092093467712, 0.9183294773101807, 0.8089151382446289, 0.9589380621910095, 0.9683259725570679, 0.9022980332374573], "confidence_by_field": 0.8089151382446289, "original_text": "硬化促進剤1"}, {"location": [[236, 520], [295, 520], [295, 542], [236, 542]], "type": "textline", "text": "着色剤", "confidence_by_character": [0.9404123425483704, 0.9459457397460938, 0.9636843204498291], "confidence_by_field": 0.9404123425483704, "original_text": "着色剤"}, {"location": [[236, 489], [295, 489], [295, 512], [236, 512]], "type": "textline", "text": "離型剤", "confidence_by_character": [0.9581624865531921, 0.945020318031311, 0.9589981436729431], "confidence_by_field": 0.945020318031311, "original_text": "離型剤"}, {"location": [[237, 459], [371, 459], [371, 480], [237, 480]], "type": "textline", "text": "カップリング剤", "confidence_by_character": [0.9071572422981262, 0.9223423600196838, 0.9463334083557129, 0.9168652892112732, 0.921983003616333, 0.9373961091041565, 0.9660329818725586], "confidence_by_field": 0.9071572422981262, "original_text": "カップリング剤"}, {"location": [[237, 429], [333, 429], [333, 448], [237, 448]], "type": "textline", "text": "硬化促進剤", "confidence_by_character": [0.9770804643630981, 0.9250267744064331, 0.831341564655304, 0.9542734026908875, 0.9714828729629517], "confidence_by_field": 0.831341564655304, "original_text": "硬化促進剤"}, {"location": [[384, 398], [491, 398], [491, 417], [384, 417]], "type": "textline", "text": "無機充填剤2", "confidence_by_character": [0.9506935477256775, 0.931281566619873, 0.9721326231956482, 0.9734131693840027, 0.9598016738891602, 0.915614664554596], "confidence_by_field": 0.915614664554596, "original_text": "無機充填剤2"}, {"location": [[592, 410], [652, 410], [652, 434], [592, 434]], "type": "textline", "text": "質量%", "confidence_by_character": [0.9058261513710022, 0.9396486282348633, 0.9175691604614258], "confidence_by_field": 0.9058261513710022, "original_text": "質量%"}, {"location": [[883, 397], [910, 397], [910, 419], [883, 419]], "type": "textline", "text": "10", "confidence_by_character": [0.9157432913780212, 0.9219378232955933], "confidence_by_field": 0.9157432913780212, "original_text": "10"}, {"location": [[970, 396], [1002, 396], [1002, 421], [970, 421]], "type": "textline", "text": "10", "confidence_by_character": [0.9164617657661438, 0.9216349720954895], "confidence_by_field": 0.9164617657661438, "original_text": "10"}, {"location": [[1061, 396], [1093, 396], [1093, 421], [1061, 421]], "type": "textline", "text": "10", "confidence_by_character": [0.9159533977508545, 0.9209455251693726], "confidence_by_field": 0.9159533977508545, "original_text": "10"}, {"location": [[1151, 396], [1183, 396], [1183, 421], [1151, 421]], "type": "textline", "text": "10", "confidence_by_character": [0.9155728816986084, 0.9205871224403381], "confidence_by_field": 0.9155728816986084, "original_text": "10"}, {"location": [[1243, 397], [1270, 397], [1270, 419], [1243, 419]], "type": "textline", "text": "10", "confidence_by_character": [0.9161996841430664, 0.9221776127815247], "confidence_by_field": 0.9161996841430664, "original_text": "10"}, {"location": [[385, 459], [532, 459], [532, 479], [385, 479]], "type": "textline", "text": "カップリング剤1", "confidence_by_character": [0.906684398651123, 0.9242702126502991, 0.9463043808937073, 0.9182814955711365, 0.9192276000976562, 0.9397355914115906, 0.958014190196991, 0.9101974368095398], "confidence_by_field": 0.906684398651123, "original_text": "カップリング剤1"}, {"location": [[384, 520], [457, 520], [457, 542], [384, 542]], "type": "textline", "text": "着色剤1", "confidence_by_character": [0.9345429539680481, 0.9415675401687622, 0.9616525769233704, 0.9126831889152527], "confidence_by_field": 0.9126831889152527, "original_text": "着色剤1"}, {"location": [[384, 491], [457, 491], [457, 510], [384, 510]], "type": "textline", "text": "離型剤1", "confidence_by_character": [0.938474178314209, 0.9532917737960815, 0.9655312895774841, 0.9134681224822998], "confidence_by_field": 0.9134681224822998, "original_text": "離型剤1"}, {"location": [[697, 458], [734, 458], [734, 483], [697, 483]], "type": "textline", "text": "0.2", "confidence_by_character": [0.918220579624176, 0.9233868718147278, 0.9168131947517395], "confidence_by_field": 0.9168131947517395, "original_text": "0.2"}, {"location": [[697, 427], [734, 427], [734, 451], [697, 451]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9187952280044556, 0.9251962304115295, 0.9168661236763], "confidence_by_field": 0.9168661236763, "original_text": "0.2"}, {"location": [[878, 427], [915, 427], [915, 451], [878, 451]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9180716276168823, 0.9220828413963318, 0.9168251156806946], "confidence_by_field": 0.9168251156806946, "original_text": "0.2"}, {"location": [[697, 520], [734, 520], [734, 543], [697, 543]], "type": "textline", "text": "0.4", "confidence_by_character": [0.9189480543136597, 0.9261084794998169, 0.9156019687652588], "confidence_by_field": 0.9156019687652588, "original_text": "0.4"}, {"location": [[699, 488], [734, 488], [734, 513], [699, 513]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9182897210121155, 0.9248169660568237, 0.9167609214782715], "confidence_by_field": 0.9167609214782715, "original_text": "0.2"}, {"location": [[788, 456], [825, 456], [825, 481], [788, 481]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9177680015563965, 0.9243178963661194, 0.9165362119674683], "confidence_by_field": 0.9165362119674683, "original_text": "0.2"}, {"location": [[788, 427], [825, 427], [825, 451], [788, 451]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9185940027236938, 0.922125518321991, 0.9162130951881409], "confidence_by_field": 0.9162130951881409, "original_text": "0.2"}, {"location": [[793, 397], [820, 397], [820, 419], [793, 419]], "type": "textline", "text": "10", "confidence_by_character": [0.9177662134170532, 0.9242537021636963], "confidence_by_field": 0.9177662134170532, "original_text": "10"}, {"location": [[789, 488], [825, 488], [825, 513], [789, 513]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9175012111663818, 0.9248098731040955, 0.9154093265533447], "confidence_by_field": 0.9154093265533447, "original_text": "0.2"}, {"location": [[878, 458], [914, 458], [914, 483], [878, 483]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9184542298316956, 0.9236595034599304, 0.9165345430374146], "confidence_by_field": 0.9165345430374146, "original_text": "0.2"}, {"location": [[969, 456], [1006, 456], [1006, 481], [969, 481]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9164131879806519, 0.9254051446914673, 0.9154728651046753], "confidence_by_field": 0.9154728651046753, "original_text": "0.2"}, {"location": [[969, 426], [1006, 426], [1006, 451], [969, 451]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9165401458740234, 0.9244502782821655, 0.9159473776817322], "confidence_by_field": 0.9159473776817322, "original_text": "0.2"}, {"location": [[1057, 456], [1094, 456], [1094, 481], [1057, 481]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9178974628448486, 0.9235621690750122, 0.9160559177398682], "confidence_by_field": 0.9160559177398682, "original_text": "0.2"}, {"location": [[1057, 426], [1094, 426], [1094, 451], [1057, 451]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9180322885513306, 0.921731173992157, 0.9166355133056641], "confidence_by_field": 0.9166355133056641, "original_text": "0.2"}, {"location": [[1148, 456], [1185, 456], [1185, 483], [1148, 483]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9164595603942871, 0.9242724180221558, 0.9157667756080627], "confidence_by_field": 0.9157667756080627, "original_text": "0.2"}, {"location": [[1148, 426], [1185, 426], [1185, 451], [1148, 451]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9180106520652771, 0.9206836223602295, 0.9163994789123535], "confidence_by_field": 0.9163994789123535, "original_text": "0.2"}, {"location": [[1238, 456], [1274, 456], [1274, 481], [1238, 481]], "type": "textline", "text": "0.2", "confidence_by_character": [0.917959451675415, 0.9236196279525757, 0.916109561920166], "confidence_by_field": 0.916109561920166, "original_text": "0.2"}, {"location": [[1238, 426], [1275, 426], [1275, 451], [1238, 451]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9176772832870483, 0.9209798574447632, 0.9162566065788269], "confidence_by_field": 0.9162566065788269, "original_text": "0.2"}, {"location": [[791, 521], [825, 521], [825, 543], [791, 543]], "type": "textline", "text": "0.4", "confidence_by_character": [0.9188475012779236, 0.9243553280830383, 0.9138782024383545], "confidence_by_field": 0.9138782024383545, "original_text": "0.4"}, {"location": [[878, 518], [914, 518], [914, 543], [878, 543]], "type": "textline", "text": "0.4", "confidence_by_character": [0.9186568856239319, 0.924356997013092, 0.9140645265579224], "confidence_by_field": 0.9140645265579224, "original_text": "0.4"}, {"location": [[878, 488], [914, 488], [914, 513], [878, 513]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9191107749938965, 0.9236589670181274, 0.9163731336593628], "confidence_by_field": 0.9163731336593628, "original_text": "0.2"}, {"location": [[969, 518], [1006, 518], [1006, 543], [969, 543]], "type": "textline", "text": "0.4", "confidence_by_character": [0.9177034497261047, 0.9257544279098511, 0.911573052406311], "confidence_by_field": 0.911573052406311, "original_text": "0.4"}, {"location": [[969, 488], [1006, 488], [1006, 513], [969, 513]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9173431992530823, 0.9257070422172546, 0.9158886075019836], "confidence_by_field": 0.9158886075019836, "original_text": "0.2"}, {"location": [[1057, 488], [1094, 488], [1094, 513], [1057, 513]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9179250001907349, 0.9226782917976379, 0.9163774251937866], "confidence_by_field": 0.9163774251937866, "original_text": "0.2"}, {"location": [[1059, 518], [1094, 518], [1094, 543], [1059, 543]], "type": "textline", "text": "0.4", "confidence_by_character": [0.9183651208877563, 0.9259626865386963, 0.9120912551879883], "confidence_by_field": 0.9120912551879883, "original_text": "0.4"}, {"location": [[1148, 488], [1185, 488], [1185, 513], [1148, 513]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9188930988311768, 0.922532856464386, 0.91632080078125], "confidence_by_field": 0.91632080078125, "original_text": "0.2"}, {"location": [[1149, 518], [1185, 518], [1185, 543], [1149, 543]], "type": "textline", "text": "0.4", "confidence_by_character": [0.9181385040283203, 0.9250556230545044, 0.9124614000320435], "confidence_by_field": 0.9124614000320435, "original_text": "0.4"}, {"location": [[1238, 518], [1274, 518], [1274, 543], [1238, 543]], "type": "textline", "text": "0.4", "confidence_by_character": [0.9189369678497314, 0.924277663230896, 0.9133647680282593], "confidence_by_field": 0.9133647680282593, "original_text": "0.4"}, {"location": [[1238, 488], [1274, 488], [1274, 513], [1238, 513]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9190401434898376, 0.9235551357269287, 0.916479766368866], "confidence_by_field": 0.916479766368866, "original_text": "0.2"}, {"location": [[383, 551], [423, 551], [423, 571], [383, 571]], "type": "textline", "text": "合計", "confidence_by_character": [0.9172796607017517, 0.9246873259544373], "confidence_by_field": 0.9172796607017517, "original_text": "合計"}, {"location": [[696, 550], [736, 550], [736, 574], [696, 574]], "type": "textline", "text": "100", "confidence_by_character": [0.9186745285987854, 0.9217865467071533, 0.9134313464164734], "confidence_by_field": 0.9134313464164734, "original_text": "100"}, {"location": [[786, 548], [826, 548], [826, 574], [786, 574]], "type": "textline", "text": "100", "confidence_by_character": [0.9187353849411011, 0.9226377010345459, 0.9132767915725708], "confidence_by_field": 0.9132767915725708, "original_text": "100"}, {"location": [[877, 548], [917, 548], [917, 574], [877, 574]], "type": "textline", "text": "100", "confidence_by_character": [0.918876051902771, 0.9223041534423828, 0.9158591628074646], "confidence_by_field": 0.9158591628074646, "original_text": "100"}, {"location": [[965, 548], [1007, 548], [1007, 574], [965, 574]], "type": "textline", "text": "100", "confidence_by_character": [0.9187213182449341, 0.9187850952148438, 0.9130398035049438], "confidence_by_field": 0.9130398035049438, "original_text": "100"}, {"location": [[1056, 548], [1096, 548], [1096, 574], [1056, 574]], "type": "textline", "text": "100", "confidence_by_character": [0.9191818237304688, 0.9226840734481812, 0.9121555089950562], "confidence_by_field": 0.9121555089950562, "original_text": "100"}, {"location": [[1146, 548], [1186, 548], [1186, 574], [1146, 574]], "type": "textline", "text": "100", "confidence_by_character": [0.9189720749855042, 0.9227191805839539, 0.9106909036636353], "confidence_by_field": 0.9106909036636353, "original_text": "100"}, {"location": [[1237, 550], [1277, 550], [1277, 574], [1237, 574]], "type": "textline", "text": "100", "confidence_by_character": [0.9188445806503296, 0.9203974008560181, 0.9133830666542053], "confidence_by_field": 0.9133830666542053, "original_text": "100"}, {"location": [[797, 763], [828, 763], [828, 793], [797, 793]], "type": "textline", "text": "10", "confidence_by_character": [0.9181389808654785, 0.9251900911331177], "confidence_by_field": 0.9181389808654785, "original_text": "10"}, {"location": [[862, 736], [938, 736], [938, 758], [862, 758]], "type": "textline", "text": "実施例3", "confidence_by_character": [0.940096378326416, 0.9323399066925049, 0.7783450484275818, 0.8894999623298645], "confidence_by_field": 0.7783450484275818, "original_text": "実施例3"}, {"location": [[886, 763], [919, 763], [919, 791], [886, 791]], "type": "textline", "text": "10", "confidence_by_character": [0.915480375289917, 0.92236328125], "confidence_by_field": 0.915480375289917, "original_text": "10"}, {"location": [[1225, 736], [1299, 736], [1299, 758], [1225, 758]], "type": "textline", "text": "比較例4", "confidence_by_character": [0.9564768671989441, 0.9437181949615479, 0.8032115697860718, 0.9161063432693481], "confidence_by_field": 0.8032115697860718, "original_text": "比較例4"}, {"location": [[243, 831], [358, 831], [358, 851], [243, 851]], "type": "textline", "text": "熱硬化性樹脂", "confidence_by_character": [0.9673244953155518, 0.9746560454368591, 0.9246954917907715, 0.9313607811927795, 0.9588099122047424, 0.9608355164527893], "confidence_by_field": 0.9246954917907715, "original_text": "熱硬化性樹脂"}, {"location": [[391, 864], [534, 864], [534, 883], [391, 883]], "type": "textline", "text": "シアネート樹脂1", "confidence_by_character": [0.9139906764030457, 0.9277936220169067, 0.9299982190132141, 0.9196512699127197, 0.9228595495223999, 0.9653444886207581, 0.9586682319641113, 0.9014871120452881], "confidence_by_field": 0.9014871120452881, "original_text": "シアネート樹脂1"}, {"location": [[391, 832], [515, 832], [515, 852], [391, 852]], "type": "textline", "text": "エポキシ樹脂3", "confidence_by_character": [0.9210723042488098, 0.9120734930038452, 0.8719823360443115, 0.9054407477378845, 0.9484961628913879, 0.955137312412262, 0.9074426889419556], "confidence_by_field": 0.8719823360443115, "original_text": "エポキシ樹脂3"}, {"location": [[391, 801], [515, 801], [515, 821], [391, 821]], "type": "textline", "text": "エポキシ樹脂2", "confidence_by_character": [0.9195655584335327, 0.8951327800750732, 0.8749004602432251, 0.9026333689689636, 0.9528785347938538, 0.9567875266075134, 0.9132635593414307], "confidence_by_field": 0.8749004602432251, "original_text": "エポキシ樹脂2"}, {"location": [[391, 769], [512, 769], [512, 789], [391, 789]], "type": "textline", "text": "エポキシ樹脂1", "confidence_by_character": [0.9198406338691711, 0.9114395380020142, 0.8742417097091675, 0.9063976407051086, 0.946216344833374, 0.9661325216293335, 0.8822324275970459], "confidence_by_field": 0.8742417097091675, "original_text": "エポキシ樹脂1"}, {"location": [[605, 734], [649, 734], [649, 760], [605, 760]], "type": "textline", "text": "単位", "confidence_by_character": [0.9275341629981995, 0.9182473421096802], "confidence_by_field": 0.9182473421096802, "original_text": "単位"}, {"location": [[681, 736], [759, 736], [759, 760], [681, 760]], "type": "textline", "text": "実施例1", "confidence_by_character": [0.9462378621101379, 0.9295192360877991, 0.7775577306747437, 0.7519926428794861], "confidence_by_field": 0.7519926428794861, "original_text": "実施例1"}, {"location": [[772, 736], [847, 736], [847, 760], [772, 760]], "type": "textline", "text": "実施例2", "confidence_by_character": [0.945273756980896, 0.9234545826911926, 0.7456976771354675, 0.8920645117759705], "confidence_by_field": 0.7456976771354675, "original_text": "実施例2"}, {"location": [[956, 736], [1027, 736], [1027, 758], [956, 758]], "type": "textline", "text": "比較例1", "confidence_by_character": [0.9605406522750854, 0.9484114050865173, 0.8022181391716003, 0.9111186861991882], "confidence_by_field": 0.8022181391716003, "original_text": "比較例1"}, {"location": [[1046, 736], [1119, 736], [1119, 758], [1046, 758]], "type": "textline", "text": "比較例2", "confidence_by_character": [0.939124345779419, 0.9446831941604614, 0.7813613414764404, 0.9183083176612854], "confidence_by_field": 0.7813613414764404, "original_text": "比較例2"}, {"location": [[1136, 736], [1209, 736], [1209, 758], [1136, 758]], "type": "textline", "text": "比較例3", "confidence_by_character": [0.9410742521286011, 0.9363664984703064, 0.8050448894500732, 0.912356972694397], "confidence_by_field": 0.8050448894500732, "original_text": "比較例3"}, {"location": [[710, 799], [731, 799], [731, 823], [710, 823]], "type": "textline", "text": "5", "confidence_by_character": [0.9124702215194702], "confidence_by_field": 0.9124702215194702, "original_text": "5"}, {"location": [[1161, 798], [1183, 798], [1183, 823], [1161, 823]], "type": "textline", "text": "5", "confidence_by_character": [0.9127680659294128], "confidence_by_field": 0.9127680659294128, "original_text": "5"}, {"location": [[612, 874], [641, 874], [641, 901], [612, 901]], "type": "textline", "text": "%", "confidence_by_character": [0.9244439005851746], "confidence_by_field": 0.9244439005851746, "original_text": "%"}, {"location": [[705, 861], [738, 861], [738, 887], [705, 887]], "type": "textline", "text": "10", "confidence_by_character": [0.9159777164459229, 0.9210568070411682], "confidence_by_field": 0.9159777164459229, "original_text": "10"}, {"location": [[710, 829], [731, 829], [731, 853], [710, 853]], "type": "textline", "text": "5", "confidence_by_character": [0.9128246903419495], "confidence_by_field": 0.9128246903419495, "original_text": "5"}, {"location": [[710, 895], [731, 895], [731, 917], [710, 917]], "type": "textline", "text": "5", "confidence_by_character": [0.913021445274353], "confidence_by_field": 0.913021445274353, "original_text": "5"}, {"location": [[797, 863], [825, 863], [825, 885], [797, 885]], "type": "textline", "text": "10", "confidence_by_character": [0.9169679284095764, 0.9230868220329285], "confidence_by_field": 0.9169679284095764, "original_text": "10"}, {"location": [[804, 834], [820, 834], [820, 852], [804, 852]], "type": "textline", "text": "-", "confidence_by_character": [0.9069211483001709], "confidence_by_field": 0.9069211483001709, "original_text": "-"}, {"location": [[804, 804], [820, 804], [820, 822], [804, 822]], "type": "textline", "text": "-", "confidence_by_character": [0.9056602120399475], "confidence_by_field": 0.9056602120399475, "original_text": "-"}, {"location": [[894, 834], [909, 834], [909, 850], [894, 850]], "type": "textline", "text": "-", "confidence_by_character": [0.9042128920555115], "confidence_by_field": 0.9042128920555115, "original_text": "-"}, {"location": [[894, 806], [909, 806], [909, 822], [894, 822]], "type": "textline", "text": "-", "confidence_by_character": [0.9011750817298889], "confidence_by_field": 0.9011750817298889, "original_text": "-"}, {"location": [[1251, 798], [1272, 798], [1272, 823], [1251, 823]], "type": "textline", "text": "5", "confidence_by_character": [0.9124239683151245], "confidence_by_field": 0.9124239683151245, "original_text": "5"}, {"location": [[801, 895], [822, 895], [822, 917], [801, 917]], "type": "textline", "text": "5", "confidence_by_character": [0.9138578176498413], "confidence_by_field": 0.9138578176498413, "original_text": "5"}, {"location": [[888, 863], [915, 863], [915, 885], [888, 885]], "type": "textline", "text": "10", "confidence_by_character": [0.916037380695343, 0.9229444265365601], "confidence_by_field": 0.916037380695343, "original_text": "10"}, {"location": [[1161, 829], [1183, 829], [1183, 853], [1161, 853]], "type": "textline", "text": "5", "confidence_by_character": [0.913336455821991], "confidence_by_field": 0.913336455821991, "original_text": "5"}, {"location": [[891, 893], [912, 893], [912, 917], [891, 917]], "type": "textline", "text": "5", "confidence_by_character": [0.9128530025482178], "confidence_by_field": 0.9128530025482178, "original_text": "5"}, {"location": [[1156, 860], [1190, 860], [1190, 887], [1156, 887]], "type": "textline", "text": "10", "confidence_by_character": [0.9161958694458008, 0.922942042350769], "confidence_by_field": 0.9161958694458008, "original_text": "10"}, {"location": [[1157, 889], [1185, 889], [1185, 920], [1157, 920]], "type": "textline", "text": "5", "confidence_by_character": [0.9128023386001587], "confidence_by_field": 0.9128023386001587, "original_text": "5"}, {"location": [[1246, 860], [1278, 860], [1278, 887], [1246, 887]], "type": "textline", "text": "10", "confidence_by_character": [0.9154711961746216, 0.9222509860992432], "confidence_by_field": 0.9154711961746216, "original_text": "10"}, {"location": [[1249, 829], [1272, 829], [1272, 853], [1249, 853]], "type": "textline", "text": "5", "confidence_by_character": [0.9134175777435303], "confidence_by_field": 0.9134175777435303, "original_text": "5"}, {"location": [[1251, 893], [1272, 893], [1272, 918], [1251, 918]], "type": "textline", "text": "5", "confidence_by_character": [0.912886381149292], "confidence_by_field": 0.912886381149292, "original_text": "5"}, {"location": [[242, 958], [338, 958], [338, 978], [242, 978]], "type": "textline", "text": "無機充填材", "confidence_by_character": [0.9516746997833252, 0.9333645105361938, 0.9753089547157288, 0.9772308468818665, 0.961030125617981], "confidence_by_field": 0.9333645105361938, "original_text": "無機充填材"}, {"location": [[242, 927], [320, 927], [320, 946], [242, 946]], "type": "textline", "text": "硬化触媒", "confidence_by_character": [0.9817054271697998, 0.9334205985069275, 0.9472013115882874, 0.9651457667350769], "confidence_by_field": 0.9334205985069275, "original_text": "硬化触媒"}, {"location": [[392, 895], [571, 895], [571, 915], [392, 915]], "type": "textline", "text": "フェノール系硬化剤1", "confidence_by_character": [0.8958667516708374, 0.8963339328765869, 0.918157160282135, 0.9283046126365662, 0.9119880199432373, 0.9494227766990662, 0.9859360456466675, 0.9373766779899597, 0.948577344417572, 0.9160327911376953], "confidence_by_field": 0.8958667516708374, "original_text": "フェノール系硬化剤1"}, {"location": [[390, 958], [496, 958], [496, 978], [390, 978]], "type": "textline", "text": "無機充填材1", "confidence_by_character": [0.955777108669281, 0.9075387120246887, 0.9792174100875854, 0.9780659079551697, 0.9546536803245544, 0.9078008532524109], "confidence_by_field": 0.9075387120246887, "original_text": "無機充填材1"}, {"location": [[390, 927], [477, 927], [477, 946], [390, 946]], "type": "textline", "text": "硬化触媒1", "confidence_by_character": [0.9808465838432312, 0.9380431771278381, 0.9594202041625977, 0.9618983864784241, 0.9127021431922913], "confidence_by_field": 0.9127021431922913, "original_text": "硬化触媒1"}, {"location": [[705, 926], [739, 926], [739, 949], [705, 949]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9183443188667297, 0.9227449893951416, 0.9166637063026428], "confidence_by_field": 0.9166637063026428, "original_text": "0.2"}, {"location": [[959, 939], [1025, 939], [1025, 963], [959, 963]], "type": "textline", "text": "(セラミック)", "confidence_by_character": [0.916853666305542, 0.9260740876197815, 0.9092920422554016, 0.9257704615592957, 0.9260854721069336, 0.9179700613021851, 0.9244228005409241], "confidence_by_field": 0.9092920422554016, "original_text": "(セラミック)"}, {"location": [[1049, 939], [1115, 939], [1115, 963], [1049, 963]], "type": "textline", "text": "(セラミック)", "confidence_by_character": [0.9169306755065918, 0.9260130524635315, 0.9072588682174683, 0.9243903160095215, 0.925151526927948, 0.9165183901786804, 0.9235830307006836], "confidence_by_field": 0.9072588682174683, "original_text": "(セラミック)"}, {"location": [[273, 1052], [489, 1052], [489, 1071], [273, 1071]], "type": "textline", "text": "L125D-L125U", "confidence_by_character": [0.9245724678039551, 0.9132282733917236, 0.906276524066925, 0.9224117398262024, 0.9258933067321777, 0.9184830784797668, 0.9157562255859375, 0.9087009429931641, 0.912157416343689, 0.9245545268058777, 0.8954219222068787], "confidence_by_field": 0.8954219222068787, "original_text": "L125D-L125U"}, {"location": [[277, 1020], [458, 1020], [458, 1039], [277, 1039]], "type": "textline", "text": "IL1TD-L1TU", "confidence_by_character": [0.8654385209083557, 0.9101585149765015, 0.9015868306159973, 0.8901948928833008, 0.9379502534866333, 0.9125338792800903, 0.9176797866821289, 0.8999291658401489, 0.9107672572135925, 0.9229012131690979], "confidence_by_field": 0.8654385209083557, "original_text": "IL1TD-L1TU"}, {"location": [[386, 987], [431, 987], [431, 1011], [386, 1011]], "type": "textline", "text": "合計", "confidence_by_character": [0.9146950840950012, 0.9339513182640076], "confidence_by_field": 0.9146950840950012, "original_text": "合計"}, {"location": [[701, 958], [743, 958], [743, 977], [701, 977]], "type": "textline", "text": "74.8", "confidence_by_character": [0.9160793423652649, 0.9184203147888184, 0.9271222949028015, 0.9212100505828857], "confidence_by_field": 0.9160793423652649, "original_text": "74.8"}, {"location": [[792, 958], [831, 958], [831, 977], [792, 977]], "type": "textline", "text": "74.8", "confidence_by_character": [0.9163533449172974, 0.9201879501342773, 0.9259200692176819, 0.9224289059638977], "confidence_by_field": 0.9163533449172974, "original_text": "74.8"}, {"location": [[796, 926], [826, 926], [826, 945], [796, 945]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9181907176971436, 0.9227388501167297, 0.9164943099021912], "confidence_by_field": 0.9164943099021912, "original_text": "0.2"}, {"location": [[886, 926], [917, 926], [917, 945], [886, 945]], "type": "textline", "text": "0.2", "confidence_by_character": [0.917766809463501, 0.9248180985450745, 0.916694164276123], "confidence_by_field": 0.916694164276123, "original_text": "0.2"}, {"location": [[1153, 923], [1190, 923], [1190, 949], [1153, 949]], "type": "textline", "text": "0.2", "confidence_by_character": [0.9172568917274475, 0.923073947429657, 0.9173568487167358], "confidence_by_field": 0.9172568917274475, "original_text": "0.2"}, {"location": [[1243, 923], [1280, 923], [1280, 949], [1243, 949]], "type": "textline", "text": "0.2", "confidence_by_character": [0.918009340763092, 0.9230136871337891, 0.9172936081886292], "confidence_by_field": 0.9172936081886292, "original_text": "0.2"}, {"location": [[603, 1021], [649, 1021], [649, 1044], [603, 1044]], "type": "textline", "text": "ppm", "confidence_by_character": [0.93487948179245, 0.9136003255844116, 0.9295913577079773], "confidence_by_field": 0.9136003255844116, "original_text": "ppm"}, {"location": [[702, 1019], [741, 1019], [741, 1042], [702, 1042]], "type": "textline", "text": "510", "confidence_by_character": [0.9160666465759277, 0.9195601344108582, 0.9182965755462646], "confidence_by_field": 0.9160666465759277, "original_text": "510"}, {"location": [[704, 990], [739, 990], [739, 1009], [704, 1009]], "type": "textline", "text": "100", "confidence_by_character": [0.9155290126800537, 0.9208177924156189, 0.9127534031867981], "confidence_by_field": 0.9127534031867981, "original_text": "100"}, {"location": [[794, 990], [830, 990], [830, 1009], [794, 1009]], "type": "textline", "text": "100", "confidence_by_character": [0.9162766337394714, 0.9207051396369934, 0.9104440212249756], "confidence_by_field": 0.9104440212249756, "original_text": "100"}, {"location": [[881, 958], [922, 958], [922, 977], [881, 977]], "type": "textline", "text": "74.8", "confidence_by_character": [0.9179209470748901, 0.919108510017395, 0.9271717667579651, 0.9218475222587585], "confidence_by_field": 0.9179209470748901, "original_text": "74.8"}, {"location": [[1149, 957], [1195, 957], [1195, 980], [1149, 980]], "type": "textline", "text": "74.8", "confidence_by_character": [0.91691654920578, 0.9166283011436462, 0.9277328848838806, 0.9221088886260986], "confidence_by_field": 0.9166283011436462, "original_text": "74.8"}, {"location": [[881, 1019], [920, 1019], [920, 1042], [881, 1042]], "type": "textline", "text": "470", "confidence_by_character": [0.9185625314712524, 0.9136890769004822, 0.9183825254440308], "confidence_by_field": 0.9136890769004822, "original_text": "470"}, {"location": [[883, 990], [920, 990], [920, 1009], [883, 1009]], "type": "textline", "text": "100", "confidence_by_character": [0.9160255789756775, 0.9194254279136658, 0.9109370708465576], "confidence_by_field": 0.9109370708465576, "original_text": "100"}, {"location": [[1151, 987], [1193, 987], [1193, 1012], [1151, 1012]], "type": "textline", "text": "100", "confidence_by_character": [0.9162431955337524, 0.9209614992141724, 0.9097722768783569], "confidence_by_field": 0.9097722768783569, "original_text": "100"}, {"location": [[1240, 957], [1285, 957], [1285, 980], [1240, 980]], "type": "textline", "text": "74.8", "confidence_by_character": [0.9182584881782532, 0.92033451795578, 0.9307538270950317, 0.9210019111633301], "confidence_by_field": 0.9182584881782532, "original_text": "74.8"}, {"location": [[1241, 1019], [1282, 1019], [1282, 1042], [1241, 1042]], "type": "textline", "text": "510", "confidence_by_character": [0.914992094039917, 0.9192731976509094, 0.9183305501937866], "confidence_by_field": 0.914992094039917, "original_text": "510"}, {"location": [[1241, 988], [1282, 988], [1282, 1012], [1241, 1012]], "type": "textline", "text": "100", "confidence_by_character": [0.9163695573806763, 0.9216129779815674, 0.9101614952087402], "confidence_by_field": 0.9101614952087402, "original_text": "100"}, {"location": [[278, 1082], [413, 1082], [413, 1103], [278, 1103]], "type": "textline", "text": "ガラス転移温度", "confidence_by_character": [0.9343377947807312, 0.920525074005127, 0.9253968000411987, 0.9179226756095886, 0.9002148509025574, 0.9482362270355225, 0.9286996722221375], "confidence_by_field": 0.9002148509025574, "original_text": "ガラス転移温度"}, {"location": [[420, 1082], [458, 1082], [458, 1104], [420, 1104]], "type": "textline", "text": "(Tg)", "confidence_by_character": [0.9211322665214539, 0.8978011012077332, 0.9115868210792542, 0.9274181723594666], "confidence_by_field": 0.8978011012077332, "original_text": "(Tg)"}, {"location": [[605, 1051], [650, 1051], [650, 1075], [605, 1075]], "type": "textline", "text": "ppm", "confidence_by_character": [0.9387204647064209, 0.9056726098060608, 0.9325636029243469], "confidence_by_field": 0.9056726098060608, "original_text": "ppm"}, {"location": [[617, 1082], [641, 1082], [641, 1104], [617, 1104]], "type": "textline", "text": "°C", "confidence_by_character": [0.9452391266822815, 0.9272077679634094], "confidence_by_field": 0.9272077679634094, "original_text": "°C"}, {"location": [[704, 1052], [739, 1052], [739, 1071], [704, 1071]], "type": "textline", "text": "390", "confidence_by_character": [0.9161151647567749, 0.9135255217552185, 0.9237526655197144], "confidence_by_field": 0.9135255217552185, "original_text": "390"}, {"location": [[793, 1084], [828, 1084], [828, 1103], [793, 1103]], "type": "textline", "text": "224", "confidence_by_character": [0.9199969172477722, 0.9111586213111877, 0.9230489730834961], "confidence_by_field": 0.9111586213111877, "original_text": "224"}, {"location": [[793, 1052], [830, 1052], [830, 1071], [793, 1071]], "type": "textline", "text": "330", "confidence_by_character": [0.9186529517173767, 0.913848340511322, 0.9239091277122498], "confidence_by_field": 0.913848340511322, "original_text": "330"}, {"location": [[793, 1019], [831, 1019], [831, 1042], [793, 1042]], "type": "textline", "text": "470", "confidence_by_character": [0.9227962493896484, 0.9154515862464905, 0.9186294078826904], "confidence_by_field": 0.9154515862464905, "original_text": "470"}, {"location": [[1151, 1019], [1191, 1019], [1191, 1044], [1151, 1044]], "type": "textline", "text": "510", "confidence_by_character": [0.919104814529419, 0.9196509122848511, 0.9189945459365845], "confidence_by_field": 0.9189945459365845, "original_text": "510"}, {"location": [[881, 1081], [920, 1081], [920, 1104], [881, 1104]], "type": "textline", "text": "224", "confidence_by_character": [0.9203312993049622, 0.9107922315597534, 0.9243770241737366], "confidence_by_field": 0.9107922315597534, "original_text": "224"}, {"location": [[881, 1050], [920, 1050], [920, 1074], [881, 1074]], "type": "textline", "text": "330", "confidence_by_character": [0.9171018600463867, 0.9141280055046082, 0.9222427010536194], "confidence_by_field": 0.9141280055046082, "original_text": "330"}, {"location": [[1151, 1084], [1191, 1084], [1191, 1108], [1151, 1108]], "type": "textline", "text": "235", "confidence_by_character": [0.9159690141677856, 0.9248628616333008, 0.9205185174942017], "confidence_by_field": 0.9159690141677856, "original_text": "235"}, {"location": [[1151, 1050], [1191, 1050], [1191, 1074], [1151, 1074]], "type": "textline", "text": "390", "confidence_by_character": [0.9146880507469177, 0.911052405834198, 0.9227294325828552], "confidence_by_field": 0.911052405834198, "original_text": "390"}, {"location": [[1241, 1084], [1280, 1084], [1280, 1108], [1241, 1108]], "type": "textline", "text": "235", "confidence_by_character": [0.9159569144248962, 0.9244524240493774, 0.920172393321991], "confidence_by_field": 0.9159569144248962, "original_text": "235"}, {"location": [[1241, 1050], [1282, 1050], [1282, 1074], [1241, 1074]], "type": "textline", "text": "390", "confidence_by_character": [0.9140211939811707, 0.9115845561027527, 0.9224275946617126], "confidence_by_field": 0.9115845561027527, "original_text": "390"}, {"location": [[184, 338], [205, 338], [205, 508], [184, 508]], "type": "textline", "text": "熱硬化性樹脂組成物", "confidence_by_character": [0.9959474205970764, 0.9997753500938416, 0.9999818801879883, 0.9999709129333496, 0.9999679327011108, 0.9999550580978394, 0.9999865293502808, 0.9992927312850952, 0.9999799728393555], "confidence_by_field": 0.9959474205970764, "original_text": "熱硬化性樹脂組成物"}, {"location": [[189, 804], [210, 804], [210, 976], [189, 976]], "type": "textline", "text": "熱硬化性樹脂組成物", "confidence_by_character": [0.9931052327156067, 0.9999232292175293, 0.9999855756759644, 0.9999867677688599, 0.9999822378158569, 0.9999606609344482, 0.9999895095825195, 0.9993935823440552, 0.9371008276939392], "confidence_by_field": 0.9371008276939392, "original_text": "熱硬化性樹脂組成物"}], "table": [{"location": [[1218, 1080], [1303, 1080], [1303, 1107], [1218, 1107]], "bbox": [1218, 1080, 1303, 1107], "points": [[1277, 1081], [1278, 1080], [1279, 1081], [1280, 1080], [1281, 1081], [1282, 1080], [1284, 1081], [1285, 1080], [1286, 1081], [1287, 1080], [1288, 1081], [1289, 1080], [1291, 1081], [1302, 1081], [1303, 1082], [1303, 1106], [1302, 1107], [1219, 1107], [1218, 1106], [1218, 1082], [1219, 1081]], "type": "cell", "rows": [11, 11], "columns": [10, 10], "text_list": []}, {"location": [[1128, 1080], [1213, 1080], [1213, 1107], [1128, 1107]], "bbox": [1128, 1080, 1213, 1107], "points": [[1129, 1081], [1130, 1080], [1132, 1081], [1212, 1081], [1213, 1082], [1213, 1106], [1212, 1107], [1129, 1107], [1128, 1106], [1129, 1105]], "type": "cell", "rows": [11, 11], "columns": [9, 9], "text_list": []}, {"location": [[858, 1080], [943, 1080], [943, 1107], [858, 1107]], "bbox": [858, 1080, 943, 1107], "points": [[877, 1081], [878, 1080], [879, 1081], [880, 1080], [881, 1081], [882, 1080], [884, 1081], [885, 1080], [887, 1080], [888, 1081], [889, 1080], [934, 1080], [935, 1081], [942, 1081], [943, 1082], [943, 1106], [942, 1107], [859, 1107], [858, 1106], [858, 1082], [859, 1081]], "type": "cell", "rows": [11, 11], "columns": [6, 6], "text_list": []}, {"location": [[769, 1080], [853, 1080], [853, 1107], [769, 1107]], "bbox": [769, 1080, 853, 1107], "points": [[792, 1081], [794, 1080], [795, 1081], [806, 1081], [808, 1080], [809, 1081], [811, 1081], [812, 1080], [815, 1080], [816, 1081], [817, 1080], [838, 1080], [839, 1081], [840, 1080], [842, 1081], [852, 1081], [853, 1082], [853, 1106], [852, 1107], [770, 1107], [769, 1106], [769, 1082], [770, 1081]], "type": "cell", "rows": [11, 11], "columns": [5, 5], "text_list": []}, {"location": [[678, 1080], [763, 1080], [763, 1107], [678, 1107]], "bbox": [678, 1080, 763, 1107], "points": [[704, 1081], [705, 1080], [706, 1081], [707, 1080], [708, 1081], [709, 1080], [711, 1081], [712, 1080], [713, 1081], [714, 1080], [715, 1081], [716, 1080], [718, 1081], [719, 1080], [720, 1081], [721, 1080], [722, 1081], [723, 1080], [725, 1081], [726, 1080], [727, 1081], [762, 1081], [763, 1082], [763, 1106], [762, 1107], [679, 1107], [678, 1106], [678, 1082], [679, 1081]], "type": "cell", "rows": [11, 11], "columns": [4, 4], "text_list": []}, {"location": [[577, 1080], [674, 1080], [674, 1107], [577, 1107]], "bbox": [577, 1080, 674, 1107], "points": [[622, 1081], [623, 1080], [625, 1080], [626, 1081], [628, 1081], [629, 1080], [635, 1080], [636, 1081], [672, 1081], [673, 1082], [673, 1104], [674, 1105], [674, 1106], [673, 1107], [578, 1107], [577, 1106], [577, 1082], [578, 1081]], "type": "cell", "rows": [11, 11], "columns": [3, 3], "text_list": []}, {"location": [[171, 1080], [574, 1080], [574, 1107], [171, 1107]], "bbox": [171, 1080, 574, 1107], "points": [[171, 1081], [173, 1080], [174, 1081], [198, 1081], [199, 1080], [201, 1081], [202, 1080], [221, 1080], [222, 1081], [223, 1080], [224, 1081], [225, 1080], [226, 1081], [285, 1081], [286, 1080], [288, 1080], [290, 1081], [292, 1081], [293, 1080], [297, 1080], [298, 1081], [299, 1081], [300, 1080], [312, 1080], [313, 1081], [318, 1081], [319, 1080], [320, 1081], [321, 1080], [370, 1080], [371, 1081], [374, 1081], [375, 1080], [390, 1080], [391, 1081], [394, 1081], [395, 1080], [408, 1080], [409, 1081], [410, 1080], [411, 1081], [418, 1081], [419, 1080], [421, 1081], [422, 1080], [423, 1081], [425, 1081], [426, 1080], [443, 1080], [444, 1081], [451, 1081], [452, 1080], [456, 1080], [457, 1081], [573, 1081], [574, 1082], [574, 1106], [573, 1107], [173, 1107], [171, 1106]], "type": "cell", "rows": [11, 11], "columns": [0, 2], "text_list": []}, {"location": [[1218, 1050], [1303, 1050], [1303, 1075], [1218, 1075]], "bbox": [1218, 1050, 1303, 1075], "points": [[1218, 1051], [1219, 1050], [1302, 1050], [1303, 1051], [1303, 1074], [1302, 1075], [1219, 1075], [1218, 1074]], "type": "cell", "rows": [10, 10], "columns": [10, 10], "text_list": []}, {"location": [[1129, 1050], [1213, 1050], [1213, 1075], [1129, 1075]], "bbox": [1129, 1050, 1213, 1075], "points": [[1129, 1051], [1130, 1050], [1212, 1050], [1213, 1051], [1213, 1074], [1212, 1075], [1130, 1075], [1129, 1074]], "type": "cell", "rows": [10, 10], "columns": [9, 9], "text_list": []}, {"location": [[858, 1050], [943, 1050], [943, 1075], [858, 1075]], "bbox": [858, 1050, 943, 1075], "points": [[858, 1051], [859, 1050], [942, 1050], [943, 1051], [943, 1074], [942, 1075], [859, 1075], [858, 1074]], "type": "cell", "rows": [10, 10], "columns": [6, 6], "text_list": []}, {"location": [[769, 1050], [853, 1050], [853, 1075], [769, 1075]], "bbox": [769, 1050, 853, 1075], "points": [[769, 1051], [770, 1050], [852, 1050], [853, 1051], [853, 1074], [852, 1075], [770, 1075], [769, 1074]], "type": "cell", "rows": [10, 10], "columns": [5, 5], "text_list": []}, {"location": [[678, 1050], [763, 1050], [763, 1075], [678, 1075]], "bbox": [678, 1050, 763, 1075], "points": [[678, 1051], [679, 1050], [762, 1050], [763, 1051], [763, 1074], [762, 1075], [679, 1075], [678, 1074]], "type": "cell", "rows": [10, 10], "columns": [4, 4], "text_list": []}, {"location": [[577, 1050], [673, 1050], [673, 1075], [577, 1075]], "bbox": [577, 1050, 673, 1075], "points": [[577, 1051], [578, 1050], [672, 1050], [673, 1051], [673, 1074], [672, 1075], [580, 1075], [578, 1074], [578, 1061], [577, 1060], [577, 1054], [578, 1053], [578, 1052]], "type": "cell", "rows": [10, 10], "columns": [3, 3], "text_list": []}, {"location": [[171, 1050], [574, 1050], [574, 1075], [171, 1075]], "bbox": [171, 1050, 574, 1075], "points": [[171, 1051], [173, 1050], [573, 1050], [574, 1051], [574, 1074], [573, 1075], [173, 1075], [171, 1074]], "type": "cell", "rows": [10, 10], "columns": [0, 2], "text_list": []}, {"location": [[577, 1018], [673, 1018], [673, 1044], [577, 1044]], "bbox": [577, 1018, 673, 1044], "points": [[577, 1019], [578, 1018], [672, 1018], [673, 1019], [673, 1043], [672, 1044], [580, 1044], [578, 1043], [578, 1031], [577, 1030]], "type": "cell", "rows": [9, 9], "columns": [3, 3], "text_list": []}, {"location": [[1218, 1017], [1303, 1017], [1303, 1044], [1218, 1044]], "bbox": [1218, 1017, 1303, 1044], "points": [[1244, 1018], [1245, 1017], [1289, 1017], [1291, 1018], [1292, 1017], [1293, 1018], [1302, 1018], [1303, 1019], [1303, 1043], [1302, 1044], [1219, 1044], [1218, 1043], [1218, 1019], [1219, 1018]], "type": "cell", "rows": [9, 9], "columns": [10, 10], "text_list": []}, {"location": [[1129, 1017], [1213, 1017], [1213, 1044], [1129, 1044]], "bbox": [1129, 1017, 1213, 1044], "points": [[1153, 1018], [1154, 1017], [1155, 1018], [1156, 1018], [1157, 1017], [1196, 1017], [1197, 1018], [1198, 1017], [1199, 1018], [1212, 1018], [1213, 1019], [1213, 1043], [1212, 1044], [1130, 1044], [1129, 1043], [1129, 1019], [1130, 1018]], "type": "cell", "rows": [9, 9], "columns": [9, 9], "text_list": []}, {"location": [[858, 1017], [943, 1017], [943, 1044], [858, 1044]], "bbox": [858, 1017, 943, 1044], "points": [[884, 1018], [885, 1017], [929, 1017], [930, 1018], [942, 1018], [943, 1019], [943, 1043], [942, 1044], [859, 1044], [858, 1043], [858, 1019], [859, 1018]], "type": "cell", "rows": [9, 9], "columns": [6, 6], "text_list": []}, {"location": [[769, 1017], [853, 1017], [853, 1044], [769, 1044]], "bbox": [769, 1017, 853, 1044], "points": [[795, 1018], [796, 1017], [836, 1017], [837, 1018], [838, 1017], [839, 1018], [852, 1018], [853, 1019], [853, 1043], [852, 1044], [770, 1044], [769, 1043], [769, 1019], [770, 1018]], "type": "cell", "rows": [9, 9], "columns": [5, 5], "text_list": []}, {"location": [[678, 1017], [763, 1017], [763, 1044], [678, 1044]], "bbox": [678, 1017, 763, 1044], "points": [[704, 1018], [705, 1017], [706, 1018], [707, 1017], [747, 1017], [748, 1018], [749, 1017], [750, 1018], [762, 1018], [763, 1019], [763, 1043], [762, 1044], [679, 1044], [678, 1043], [678, 1019], [679, 1018]], "type": "cell", "rows": [9, 9], "columns": [4, 4], "text_list": []}, {"location": [[171, 1017], [574, 1017], [574, 1044], [171, 1044]], "bbox": [171, 1017, 574, 1044], "points": [[273, 1018], [274, 1017], [276, 1018], [277, 1017], [366, 1017], [367, 1018], [383, 1018], [384, 1017], [396, 1017], [397, 1018], [398, 1017], [399, 1018], [401, 1017], [402, 1018], [403, 1017], [464, 1017], [465, 1018], [467, 1018], [468, 1017], [471, 1017], [472, 1018], [573, 1018], [574, 1019], [574, 1043], [573, 1044], [173, 1044], [171, 1043], [171, 1019], [173, 1018]], "type": "cell", "rows": [9, 9], "columns": [0, 2], "text_list": []}, {"location": [[1218, 987], [1303, 987], [1303, 1013], [1218, 1013]], "bbox": [1218, 987, 1303, 1013], "points": [[1218, 988], [1219, 987], [1302, 987], [1303, 988], [1303, 1012], [1302, 1013], [1219, 1013], [1218, 1012]], "type": "cell", "rows": [8, 8], "columns": [10, 10], "text_list": []}, {"location": [[1129, 987], [1213, 987], [1213, 1013], [1129, 1013]], "bbox": [1129, 987, 1213, 1013], "points": [[1129, 988], [1130, 987], [1212, 987], [1213, 988], [1213, 1012], [1212, 1013], [1130, 1013], [1129, 1012]], "type": "cell", "rows": [8, 8], "columns": [9, 9], "text_list": []}, {"location": [[858, 987], [943, 987], [943, 1013], [858, 1013]], "bbox": [858, 987, 943, 1013], "points": [[858, 988], [859, 987], [942, 987], [943, 988], [943, 1012], [942, 1013], [859, 1013], [858, 1012]], "type": "cell", "rows": [8, 8], "columns": [6, 6], "text_list": []}, {"location": [[769, 987], [853, 987], [853, 1013], [769, 1013]], "bbox": [769, 987, 853, 1013], "points": [[769, 988], [770, 987], [852, 987], [853, 988], [853, 1012], [852, 1013], [770, 1013], [769, 1012]], "type": "cell", "rows": [8, 8], "columns": [5, 5], "text_list": []}, {"location": [[678, 987], [763, 987], [763, 1013], [678, 1013]], "bbox": [678, 987, 763, 1013], "points": [[678, 988], [679, 987], [762, 987], [763, 988], [763, 1012], [762, 1013], [679, 1013], [678, 1012]], "type": "cell", "rows": [8, 8], "columns": [4, 4], "text_list": []}, {"location": [[240, 987], [574, 987], [574, 1013], [240, 1013]], "bbox": [240, 987, 574, 1013], "points": [[240, 988], [242, 987], [573, 987], [574, 988], [574, 1012], [573, 1013], [242, 1013], [240, 1012]], "type": "cell", "rows": [8, 8], "columns": [1, 2], "text_list": []}, {"location": [[240, 955], [383, 955], [383, 983], [240, 983]], "bbox": [240, 955, 383, 983], "points": [[240, 956], [242, 955], [382, 955], [383, 956], [383, 982], [382, 983], [242, 983], [240, 982]], "type": "cell", "rows": [7, 7], "columns": [1, 1], "text_list": []}, {"location": [[1218, 954], [1303, 954], [1303, 983], [1218, 983]], "bbox": [1218, 954, 1303, 983], "points": [[1249, 955], [1250, 954], [1264, 954], [1265, 955], [1302, 955], [1303, 956], [1303, 982], [1302, 983], [1219, 983], [1218, 982], [1219, 981], [1218, 980], [1218, 956], [1219, 955]], "type": "cell", "rows": [7, 7], "columns": [10, 10], "text_list": []}, {"location": [[1129, 954], [1213, 954], [1213, 983], [1129, 983]], "bbox": [1129, 954, 1213, 983], "points": [[1164, 955], [1165, 954], [1167, 955], [1168, 954], [1169, 955], [1170, 954], [1171, 955], [1173, 954], [1174, 955], [1212, 955], [1213, 956], [1213, 982], [1212, 983], [1130, 983], [1129, 982], [1129, 956], [1130, 955]], "type": "cell", "rows": [7, 7], "columns": [9, 9], "text_list": []}, {"location": [[858, 954], [943, 954], [943, 983], [858, 983]], "bbox": [858, 954, 943, 983], "points": [[888, 955], [889, 954], [904, 954], [905, 955], [942, 955], [943, 956], [943, 982], [942, 983], [859, 983], [858, 982], [858, 956], [859, 955]], "type": "cell", "rows": [7, 7], "columns": [6, 6], "text_list": []}, {"location": [[769, 954], [853, 954], [853, 983], [769, 983]], "bbox": [769, 954, 853, 983], "points": [[806, 955], [808, 954], [809, 955], [810, 954], [811, 955], [852, 955], [853, 956], [853, 982], [852, 983], [770, 983], [769, 982], [769, 956], [770, 955]], "type": "cell", "rows": [7, 7], "columns": [5, 5], "text_list": []}, {"location": [[678, 954], [763, 954], [763, 983], [678, 983]], "bbox": [678, 954, 763, 983], "points": [[713, 955], [714, 954], [715, 955], [762, 955], [763, 956], [763, 982], [762, 983], [679, 983], [678, 982], [678, 956], [679, 955]], "type": "cell", "rows": [7, 7], "columns": [4, 4], "text_list": []}, {"location": [[388, 954], [574, 954], [574, 983], [388, 983]], "bbox": [388, 954, 574, 983], "points": [[388, 955], [389, 954], [391, 954], [392, 955], [573, 955], [574, 956], [574, 982], [573, 983], [389, 983], [388, 982]], "type": "cell", "rows": [7, 7], "columns": [2, 2], "text_list": []}, {"location": [[1218, 923], [1303, 923], [1303, 950], [1218, 950]], "bbox": [1218, 923, 1303, 950], "points": [[1244, 925], [1245, 923], [1287, 923], [1288, 925], [1289, 923], [1291, 925], [1292, 923], [1293, 925], [1302, 925], [1303, 926], [1303, 949], [1302, 950], [1219, 950], [1218, 949], [1219, 948], [1218, 947], [1218, 926], [1219, 925]], "type": "cell", "rows": [6, 6], "columns": [10, 10], "text_list": []}, {"location": [[1129, 923], [1213, 923], [1213, 950], [1129, 950]], "bbox": [1129, 923, 1213, 950], "points": [[1129, 925], [1130, 923], [1132, 925], [1141, 925], [1142, 923], [1143, 925], [1144, 923], [1146, 925], [1147, 923], [1148, 925], [1149, 923], [1150, 925], [1151, 923], [1187, 923], [1188, 925], [1189, 923], [1190, 925], [1191, 923], [1192, 925], [1212, 925], [1213, 926], [1213, 949], [1212, 950], [1130, 950], [1129, 949]], "type": "cell", "rows": [6, 6], "columns": [9, 9], "text_list": []}, {"location": [[858, 923], [943, 923], [943, 950], [858, 950]], "bbox": [858, 923, 943, 950], "points": [[884, 925], [885, 923], [929, 923], [930, 925], [932, 923], [933, 925], [942, 925], [943, 926], [943, 949], [942, 950], [859, 950], [858, 949], [858, 926], [859, 925]], "type": "cell", "rows": [6, 6], "columns": [6, 6], "text_list": []}, {"location": [[769, 923], [853, 923], [853, 950], [769, 950]], "bbox": [769, 923, 853, 950], "points": [[790, 925], [791, 923], [792, 925], [794, 923], [824, 923], [825, 925], [826, 923], [828, 925], [829, 923], [830, 925], [831, 923], [832, 925], [833, 923], [835, 925], [836, 923], [837, 925], [852, 925], [853, 926], [853, 949], [852, 950], [770, 950], [769, 949], [769, 926], [770, 925]], "type": "cell", "rows": [6, 6], "columns": [5, 5], "text_list": []}, {"location": [[678, 923], [763, 923], [763, 950], [678, 950]], "bbox": [678, 923, 763, 950], "points": [[678, 925], [679, 923], [680, 925], [694, 925], [695, 923], [697, 925], [699, 925], [700, 923], [701, 925], [702, 923], [737, 923], [739, 925], [740, 923], [741, 925], [742, 923], [743, 925], [744, 923], [746, 925], [747, 923], [748, 925], [749, 923], [750, 925], [751, 923], [753, 925], [762, 925], [763, 926], [763, 949], [762, 950], [679, 950], [678, 949]], "type": "cell", "rows": [6, 6], "columns": [4, 4], "text_list": []}, {"location": [[388, 923], [574, 923], [574, 950], [388, 950]], "bbox": [388, 923, 574, 950], "points": [[388, 925], [389, 923], [390, 925], [391, 923], [410, 923], [411, 925], [414, 925], [415, 923], [422, 923], [423, 925], [425, 925], [426, 923], [429, 923], [430, 925], [431, 923], [438, 923], [439, 925], [440, 925], [442, 923], [446, 923], [447, 925], [449, 925], [450, 923], [454, 923], [456, 925], [457, 923], [490, 923], [491, 925], [492, 923], [493, 925], [494, 923], [495, 925], [497, 923], [498, 925], [499, 923], [500, 925], [501, 923], [502, 925], [504, 923], [505, 925], [506, 923], [507, 925], [508, 923], [509, 925], [511, 923], [512, 925], [513, 923], [514, 925], [515, 923], [516, 925], [518, 923], [519, 925], [520, 923], [521, 925], [522, 923], [523, 925], [525, 923], [526, 925], [527, 923], [528, 925], [529, 923], [530, 925], [532, 923], [533, 925], [534, 923], [535, 925], [536, 923], [537, 925], [539, 923], [546, 923], [547, 925], [548, 923], [549, 925], [550, 923], [552, 925], [553, 923], [554, 925], [555, 923], [556, 925], [573, 925], [574, 926], [574, 949], [573, 950], [389, 950], [388, 949]], "type": "cell", "rows": [6, 6], "columns": [2, 2], "text_list": []}, {"location": [[240, 923], [383, 923], [383, 950], [240, 950]], "bbox": [240, 923, 383, 950], "points": [[240, 925], [242, 923], [243, 925], [244, 925], [245, 923], [260, 923], [261, 925], [263, 923], [264, 925], [267, 925], [268, 923], [274, 923], [276, 925], [278, 925], [279, 923], [281, 923], [283, 925], [284, 923], [291, 923], [292, 925], [293, 925], [294, 923], [300, 923], [301, 925], [302, 923], [321, 923], [322, 925], [323, 923], [325, 925], [326, 923], [327, 925], [382, 925], [383, 926], [383, 949], [382, 950], [242, 950], [240, 949]], "type": "cell", "rows": [6, 6], "columns": [1, 1], "text_list": []}, {"location": [[1218, 893], [1303, 893], [1303, 920], [1218, 920]], "bbox": [1218, 893, 1303, 920], "points": [[1218, 894], [1219, 893], [1302, 893], [1303, 894], [1303, 919], [1302, 920], [1219, 920], [1218, 919]], "type": "cell", "rows": [5, 5], "columns": [10, 10], "text_list": []}, {"location": [[1129, 893], [1213, 893], [1213, 920], [1129, 920]], "bbox": [1129, 893, 1213, 920], "points": [[1129, 894], [1130, 893], [1212, 893], [1213, 894], [1213, 919], [1212, 920], [1130, 920], [1129, 919]], "type": "cell", "rows": [5, 5], "columns": [9, 9], "text_list": []}, {"location": [[858, 893], [943, 893], [943, 920], [858, 920]], "bbox": [858, 893, 943, 920], "points": [[858, 894], [859, 893], [942, 893], [943, 894], [943, 919], [942, 920], [859, 920], [858, 919]], "type": "cell", "rows": [5, 5], "columns": [6, 6], "text_list": []}, {"location": [[769, 893], [853, 893], [853, 920], [769, 920]], "bbox": [769, 893, 853, 920], "points": [[769, 894], [770, 893], [852, 893], [853, 894], [853, 919], [852, 920], [770, 920], [769, 919]], "type": "cell", "rows": [5, 5], "columns": [5, 5], "text_list": []}, {"location": [[678, 893], [763, 893], [763, 920], [678, 920]], "bbox": [678, 893, 763, 920], "points": [[678, 894], [679, 893], [762, 893], [763, 894], [763, 919], [762, 920], [679, 920], [678, 919], [679, 918], [678, 916]], "type": "cell", "rows": [5, 5], "columns": [4, 4], "text_list": []}, {"location": [[388, 893], [574, 893], [574, 920], [388, 920]], "bbox": [388, 893, 574, 920], "points": [[388, 894], [389, 893], [573, 893], [574, 894], [574, 919], [573, 920], [389, 920], [388, 919]], "type": "cell", "rows": [5, 5], "columns": [2, 2], "text_list": []}, {"location": [[1218, 860], [1303, 860], [1303, 887], [1218, 887]], "bbox": [1218, 860, 1303, 887], "points": [[1244, 861], [1245, 860], [1246, 861], [1247, 860], [1287, 860], [1288, 861], [1289, 860], [1291, 861], [1302, 861], [1303, 863], [1303, 886], [1302, 887], [1219, 887], [1218, 886], [1218, 863], [1219, 861]], "type": "cell", "rows": [4, 4], "columns": [10, 10], "text_list": []}, {"location": [[1129, 860], [1213, 860], [1213, 887], [1129, 887]], "bbox": [1129, 860, 1213, 887], "points": [[1143, 861], [1144, 860], [1146, 861], [1147, 860], [1148, 861], [1149, 860], [1150, 861], [1151, 860], [1153, 861], [1154, 860], [1155, 861], [1156, 860], [1157, 861], [1158, 860], [1191, 860], [1192, 861], [1194, 860], [1195, 861], [1212, 861], [1213, 863], [1213, 886], [1212, 887], [1130, 887], [1129, 886], [1129, 863], [1130, 861]], "type": "cell", "rows": [4, 4], "columns": [9, 9], "text_list": []}, {"location": [[858, 860], [943, 860], [943, 887], [858, 887]], "bbox": [858, 860, 943, 887], "points": [[874, 861], [875, 860], [877, 861], [878, 860], [879, 861], [884, 861], [885, 860], [886, 861], [887, 860], [929, 860], [930, 861], [932, 860], [933, 861], [942, 861], [943, 863], [943, 886], [942, 887], [859, 887], [858, 886], [858, 863], [859, 861]], "type": "cell", "rows": [4, 4], "columns": [6, 6], "text_list": []}, {"location": [[769, 860], [853, 860], [853, 887], [769, 887]], "bbox": [769, 860, 853, 887], "points": [[795, 861], [796, 860], [797, 861], [798, 860], [831, 860], [832, 861], [833, 860], [835, 861], [852, 861], [853, 863], [853, 886], [852, 887], [770, 887], [769, 886], [769, 863], [770, 861]], "type": "cell", "rows": [4, 4], "columns": [5, 5], "text_list": []}, {"location": [[678, 860], [763, 860], [763, 887], [678, 887]], "bbox": [678, 860, 763, 887], "points": [[678, 861], [679, 860], [680, 861], [704, 861], [705, 860], [706, 861], [707, 860], [747, 860], [748, 861], [749, 860], [750, 861], [751, 860], [753, 861], [762, 861], [763, 863], [763, 886], [762, 887], [679, 887], [678, 886]], "type": "cell", "rows": [4, 4], "columns": [4, 4], "text_list": []}, {"location": [[388, 860], [574, 860], [574, 887], [388, 887]], "bbox": [388, 860, 574, 887], "points": [[388, 861], [389, 860], [392, 860], [394, 861], [411, 861], [412, 860], [414, 861], [415, 860], [416, 861], [417, 860], [548, 860], [549, 861], [550, 860], [552, 861], [553, 860], [554, 861], [555, 860], [556, 861], [557, 860], [559, 861], [560, 860], [561, 861], [573, 861], [574, 863], [574, 886], [573, 887], [389, 887], [388, 886]], "type": "cell", "rows": [4, 4], "columns": [2, 2], "text_list": []}, {"location": [[1218, 830], [1303, 830], [1303, 857], [1218, 857]], "bbox": [1218, 830, 1303, 857], "points": [[1218, 831], [1219, 830], [1302, 830], [1303, 831], [1303, 856], [1302, 857], [1219, 857], [1218, 856]], "type": "cell", "rows": [3, 3], "columns": [10, 10], "text_list": []}, {"location": [[1129, 830], [1213, 830], [1213, 857], [1129, 857]], "bbox": [1129, 830, 1213, 857], "points": [[1129, 831], [1130, 830], [1212, 830], [1213, 831], [1213, 856], [1212, 857], [1130, 857], [1129, 856]], "type": "cell", "rows": [3, 3], "columns": [9, 9], "text_list": []}, {"location": [[858, 830], [943, 830], [943, 857], [858, 857]], "bbox": [858, 830, 943, 857], "points": [[858, 831], [859, 830], [942, 830], [943, 831], [943, 856], [942, 857], [859, 857], [858, 856]], "type": "cell", "rows": [3, 3], "columns": [6, 6], "text_list": []}, {"location": [[769, 830], [853, 830], [853, 857], [769, 857]], "bbox": [769, 830, 853, 857], "points": [[769, 831], [770, 830], [852, 830], [853, 831], [853, 856], [852, 857], [770, 857], [769, 856]], "type": "cell", "rows": [3, 3], "columns": [5, 5], "text_list": []}, {"location": [[678, 830], [763, 830], [763, 857], [678, 857]], "bbox": [678, 830, 763, 857], "points": [[678, 831], [679, 830], [762, 830], [763, 831], [763, 856], [762, 857], [679, 857], [678, 856]], "type": "cell", "rows": [3, 3], "columns": [4, 4], "text_list": []}, {"location": [[388, 830], [574, 830], [574, 857], [388, 857]], "bbox": [388, 830, 574, 857], "points": [[388, 831], [389, 830], [573, 830], [574, 831], [574, 856], [573, 857], [389, 857], [388, 856]], "type": "cell", "rows": [3, 3], "columns": [2, 2], "text_list": []}, {"location": [[769, 798], [853, 798], [853, 824], [769, 824]], "bbox": [769, 798, 853, 824], "points": [[769, 799], [770, 798], [852, 798], [853, 799], [853, 823], [852, 824], [770, 824], [769, 823]], "type": "cell", "rows": [2, 2], "columns": [5, 5], "text_list": []}, {"location": [[1218, 797], [1303, 797], [1303, 824], [1218, 824]], "bbox": [1218, 797, 1303, 824], "points": [[1244, 798], [1245, 797], [1246, 798], [1247, 797], [1249, 798], [1250, 797], [1275, 797], [1277, 798], [1278, 797], [1279, 798], [1302, 798], [1303, 799], [1303, 823], [1302, 824], [1219, 824], [1218, 823], [1218, 799], [1219, 798]], "type": "cell", "rows": [2, 2], "columns": [10, 10], "text_list": []}, {"location": [[1129, 797], [1213, 797], [1213, 824], [1129, 824]], "bbox": [1129, 797, 1213, 824], "points": [[1129, 798], [1130, 797], [1132, 798], [1146, 798], [1147, 797], [1148, 798], [1149, 797], [1150, 798], [1151, 797], [1153, 798], [1154, 797], [1155, 798], [1164, 798], [1165, 797], [1167, 798], [1168, 797], [1169, 798], [1170, 797], [1171, 798], [1173, 797], [1174, 798], [1212, 798], [1213, 799], [1213, 823], [1212, 824], [1130, 824], [1129, 823]], "type": "cell", "rows": [2, 2], "columns": [9, 9], "text_list": []}, {"location": [[858, 797], [943, 797], [943, 824], [858, 824]], "bbox": [858, 797, 943, 824], "points": [[888, 798], [889, 797], [891, 798], [892, 797], [893, 798], [894, 797], [895, 798], [942, 798], [943, 799], [943, 823], [942, 824], [859, 824], [858, 823], [858, 799], [859, 798]], "type": "cell", "rows": [2, 2], "columns": [6, 6], "text_list": []}, {"location": [[678, 797], [763, 797], [763, 824], [678, 824]], "bbox": [678, 797, 763, 824], "points": [[678, 798], [679, 797], [680, 798], [704, 798], [705, 797], [706, 798], [707, 797], [708, 798], [709, 797], [711, 798], [712, 797], [733, 797], [734, 798], [735, 797], [736, 798], [762, 798], [763, 799], [763, 823], [762, 824], [679, 824], [678, 823]], "type": "cell", "rows": [2, 2], "columns": [4, 4], "text_list": []}, {"location": [[388, 797], [574, 797], [574, 824], [388, 824]], "bbox": [388, 797, 574, 824], "points": [[388, 798], [389, 797], [394, 797], [395, 798], [418, 798], [419, 797], [421, 798], [424, 798], [425, 797], [429, 797], [430, 798], [431, 797], [432, 798], [433, 797], [435, 798], [484, 798], [485, 797], [486, 798], [495, 798], [497, 797], [498, 798], [573, 798], [574, 799], [574, 823], [573, 824], [389, 824], [388, 823]], "type": "cell", "rows": [2, 2], "columns": [2, 2], "text_list": []}, {"location": [[240, 768], [383, 768], [383, 920], [240, 920]], "bbox": [240, 768, 383, 920], "points": [[240, 769], [242, 768], [382, 768], [383, 769], [383, 919], [382, 920], [242, 920], [240, 919]], "type": "cell", "rows": [1, 5], "columns": [1, 1], "text_list": []}, {"location": [[1218, 767], [1303, 767], [1303, 792], [1218, 792]], "bbox": [1218, 767, 1303, 792], "points": [[1218, 768], [1219, 767], [1220, 768], [1223, 768], [1224, 767], [1302, 767], [1303, 768], [1303, 791], [1302, 792], [1219, 792], [1218, 791]], "type": "cell", "rows": [1, 1], "columns": [10, 10], "text_list": []}, {"location": [[1128, 767], [1213, 767], [1213, 792], [1128, 792]], "bbox": [1128, 767, 1213, 792], "points": [[1130, 767], [1132, 768], [1133, 767], [1205, 767], [1206, 768], [1208, 767], [1209, 768], [1212, 768], [1213, 769], [1213, 791], [1212, 792], [1130, 792], [1129, 791], [1129, 773], [1128, 771], [1128, 769]], "type": "cell", "rows": [1, 1], "columns": [9, 9], "text_list": []}, {"location": [[1038, 767], [1123, 767], [1123, 1107], [1038, 1107]], "bbox": [1038, 767, 1123, 1107], "points": [[1038, 768], [1039, 767], [1040, 768], [1045, 768], [1046, 767], [1047, 768], [1049, 767], [1050, 768], [1051, 767], [1052, 768], [1053, 767], [1054, 768], [1068, 768], [1070, 767], [1071, 768], [1072, 767], [1073, 768], [1074, 767], [1075, 768], [1077, 767], [1078, 768], [1079, 767], [1080, 768], [1081, 767], [1082, 768], [1122, 768], [1123, 769], [1123, 1106], [1122, 1107], [1039, 1107], [1038, 1106]], "type": "cell", "rows": [1, 11], "columns": [8, 8], "text_list": []}, {"location": [[948, 767], [1033, 767], [1033, 1107], [948, 1107]], "bbox": [948, 767, 1033, 1107], "points": [[951, 768], [953, 767], [954, 768], [955, 767], [956, 768], [957, 767], [958, 768], [960, 767], [961, 768], [962, 767], [963, 768], [975, 768], [976, 767], [977, 768], [978, 767], [980, 768], [981, 767], [982, 768], [983, 767], [984, 768], [985, 767], [987, 768], [988, 767], [989, 768], [1032, 768], [1033, 769], [1033, 1106], [1032, 1107], [949, 1107], [948, 1106], [948, 1082], [949, 1081], [949, 1075], [948, 1074], [948, 1051], [949, 1050], [949, 1044], [948, 1043], [948, 1019], [949, 1018], [949, 1012], [948, 1011], [948, 988], [949, 987], [949, 982], [948, 981], [948, 956], [949, 955], [949, 950], [948, 949], [948, 926], [949, 925], [949, 919], [948, 918], [948, 894], [949, 893], [949, 887], [948, 886], [948, 863], [949, 861], [949, 856], [948, 854], [948, 831], [949, 830], [949, 824], [948, 823], [948, 799], [949, 798], [949, 792], [948, 791], [948, 769], [949, 768]], "type": "cell", "rows": [1, 11], "columns": [7, 7], "text_list": []}, {"location": [[858, 767], [943, 767], [943, 792], [858, 792]], "bbox": [858, 767, 943, 792], "points": [[858, 768], [859, 767], [860, 768], [861, 768], [863, 767], [936, 767], [937, 768], [939, 767], [940, 768], [941, 767], [943, 769], [943, 791], [942, 792], [859, 792], [858, 791]], "type": "cell", "rows": [1, 1], "columns": [6, 6], "text_list": []}, {"location": [[769, 767], [853, 767], [853, 792], [769, 792]], "bbox": [769, 767, 853, 792], "points": [[769, 768], [770, 767], [771, 768], [773, 767], [850, 767], [851, 768], [852, 768], [853, 769], [853, 791], [852, 792], [770, 792], [769, 791]], "type": "cell", "rows": [1, 1], "columns": [5, 5], "text_list": []}, {"location": [[678, 767], [763, 767], [763, 792], [678, 792]], "bbox": [678, 767, 763, 792], "points": [[678, 768], [679, 767], [680, 768], [682, 768], [684, 767], [761, 767], [763, 769], [763, 791], [762, 792], [679, 792], [678, 791]], "type": "cell", "rows": [1, 1], "columns": [4, 4], "text_list": []}, {"location": [[577, 767], [674, 767], [674, 1013], [577, 1013]], "bbox": [577, 767, 674, 1013], "points": [[624, 768], [625, 767], [626, 768], [628, 767], [630, 767], [631, 768], [632, 767], [633, 768], [635, 767], [636, 768], [673, 768], [674, 769], [673, 770], [673, 1012], [672, 1013], [578, 1013], [577, 1012], [578, 1011], [578, 988], [580, 987], [580, 983], [578, 982], [578, 955], [580, 954], [580, 950], [578, 949], [578, 925], [580, 923], [580, 920], [578, 919], [578, 893], [580, 892], [580, 888], [578, 887], [578, 861], [580, 860], [580, 857], [578, 856], [578, 830], [580, 829], [580, 824], [578, 823], [578, 798], [580, 797], [580, 792], [578, 791], [578, 769], [580, 768]], "type": "cell", "rows": [1, 8], "columns": [3, 3], "text_list": []}, {"location": [[388, 767], [574, 767], [574, 792], [388, 792]], "bbox": [388, 767, 574, 792], "points": [[388, 768], [389, 767], [390, 768], [391, 767], [562, 767], [563, 768], [564, 767], [566, 768], [573, 768], [574, 769], [574, 791], [573, 792], [389, 792], [388, 791]], "type": "cell", "rows": [1, 1], "columns": [2, 2], "text_list": []}, {"location": [[171, 767], [237, 767], [237, 1013], [171, 1013]], "bbox": [171, 767, 237, 1013], "points": [[171, 768], [173, 767], [174, 768], [236, 768], [237, 769], [237, 770], [236, 771], [236, 833], [237, 835], [236, 836], [237, 837], [236, 838], [237, 839], [236, 840], [237, 842], [236, 843], [237, 844], [236, 845], [236, 929], [237, 930], [237, 937], [236, 939], [236, 962], [237, 963], [237, 966], [236, 967], [237, 968], [236, 969], [237, 970], [236, 971], [236, 1009], [237, 1010], [237, 1012], [236, 1013], [173, 1013], [171, 1012]], "type": "cell", "rows": [1, 8], "columns": [0, 0], "text_list": []}, {"location": [[1218, 734], [1303, 734], [1303, 761], [1218, 761]], "bbox": [1218, 734, 1303, 761], "points": [[1218, 735], [1219, 734], [1220, 735], [1225, 735], [1226, 734], [1257, 734], [1258, 735], [1259, 734], [1302, 734], [1303, 735], [1303, 760], [1302, 761], [1219, 761], [1218, 760]], "type": "cell", "rows": [0, 0], "columns": [10, 10], "text_list": []}, {"location": [[1128, 734], [1213, 734], [1213, 761], [1128, 761]], "bbox": [1128, 734, 1213, 761], "points": [[1130, 734], [1132, 735], [1134, 735], [1135, 734], [1165, 734], [1167, 735], [1168, 735], [1169, 734], [1175, 734], [1176, 735], [1177, 735], [1178, 734], [1189, 734], [1190, 735], [1198, 735], [1199, 734], [1203, 734], [1204, 735], [1211, 735], [1212, 734], [1213, 735], [1213, 760], [1212, 761], [1129, 761], [1128, 760], [1128, 736]], "type": "cell", "rows": [0, 0], "columns": [9, 9], "text_list": []}, {"location": [[1038, 734], [1123, 734], [1123, 761], [1038, 761]], "bbox": [1038, 734, 1123, 761], "points": [[1038, 735], [1039, 734], [1040, 735], [1045, 735], [1046, 734], [1047, 735], [1050, 735], [1051, 734], [1056, 734], [1057, 735], [1058, 734], [1063, 734], [1064, 735], [1065, 734], [1075, 734], [1077, 735], [1078, 735], [1079, 734], [1084, 734], [1085, 735], [1088, 735], [1089, 734], [1098, 734], [1099, 735], [1104, 735], [1105, 734], [1106, 735], [1111, 735], [1112, 734], [1114, 734], [1115, 735], [1122, 735], [1123, 736], [1123, 760], [1122, 761], [1039, 761], [1038, 760]], "type": "cell", "rows": [0, 0], "columns": [8, 8], "text_list": []}, {"location": [[948, 734], [1033, 734], [1033, 761], [948, 761]], "bbox": [948, 734, 1033, 761], "points": [[954, 735], [955, 734], [957, 734], [958, 735], [960, 734], [964, 734], [966, 735], [968, 735], [969, 734], [971, 734], [973, 735], [974, 734], [975, 735], [976, 734], [978, 734], [980, 735], [981, 734], [985, 734], [987, 735], [989, 735], [990, 734], [995, 734], [996, 735], [997, 735], [998, 734], [1016, 734], [1017, 735], [1018, 734], [1019, 735], [1020, 734], [1023, 734], [1024, 735], [1025, 734], [1026, 735], [1027, 734], [1029, 735], [1030, 734], [1031, 735], [1032, 734], [1033, 735], [1033, 760], [1032, 761], [949, 761], [948, 760], [948, 736], [949, 735]], "type": "cell", "rows": [0, 0], "columns": [7, 7], "text_list": []}, {"location": [[858, 734], [943, 734], [943, 761], [858, 761]], "bbox": [858, 734, 943, 761], "points": [[858, 735], [859, 734], [860, 735], [863, 735], [864, 734], [865, 735], [866, 734], [878, 734], [879, 735], [886, 735], [887, 734], [889, 734], [891, 735], [895, 735], [897, 734], [898, 735], [900, 735], [901, 734], [902, 735], [905, 735], [906, 734], [934, 734], [935, 735], [940, 735], [941, 734], [943, 736], [943, 760], [942, 761], [859, 761], [858, 760]], "type": "cell", "rows": [0, 0], "columns": [6, 6], "text_list": []}, {"location": [[769, 734], [853, 734], [853, 761], [769, 761]], "bbox": [769, 734, 853, 761], "points": [[769, 735], [770, 734], [771, 735], [773, 734], [774, 735], [775, 734], [776, 735], [777, 734], [787, 734], [788, 735], [789, 734], [790, 735], [791, 734], [792, 735], [796, 735], [797, 734], [799, 734], [801, 735], [804, 735], [805, 734], [806, 735], [811, 735], [812, 734], [813, 735], [815, 734], [845, 734], [846, 735], [847, 734], [849, 735], [851, 735], [852, 734], [853, 735], [853, 760], [852, 761], [770, 761], [769, 760]], "type": "cell", "rows": [0, 0], "columns": [5, 5], "text_list": []}, {"location": [[678, 734], [763, 734], [763, 761], [678, 761]], "bbox": [678, 734, 763, 761], "points": [[678, 735], [679, 734], [680, 735], [685, 735], [686, 734], [698, 734], [699, 735], [706, 735], [707, 734], [709, 734], [711, 735], [715, 735], [716, 734], [718, 735], [720, 735], [721, 734], [759, 734], [760, 735], [761, 734], [762, 734], [763, 735], [763, 760], [762, 761], [679, 761], [678, 760]], "type": "cell", "rows": [0, 0], "columns": [4, 4], "text_list": []}, {"location": [[577, 734], [674, 734], [674, 761], [577, 761]], "bbox": [577, 734, 674, 761], "points": [[605, 735], [606, 734], [621, 734], [622, 735], [623, 734], [635, 734], [636, 735], [638, 735], [639, 734], [640, 735], [642, 734], [643, 735], [673, 735], [674, 736], [674, 760], [673, 761], [578, 761], [577, 760], [577, 736], [578, 735]], "type": "cell", "rows": [0, 0], "columns": [3, 3], "text_list": []}, {"location": [[171, 734], [574, 734], [574, 761], [171, 761]], "bbox": [171, 734, 574, 761], "points": [[196, 735], [197, 734], [198, 735], [199, 734], [214, 734], [215, 735], [216, 734], [217, 735], [218, 734], [219, 735], [573, 735], [574, 736], [574, 760], [573, 761], [173, 761], [171, 760], [171, 736], [173, 735]], "type": "cell", "rows": [0, 0], "columns": [0, 2], "text_list": []}, {"location": [[166, 729], [1309, 729], [1309, 1113], [166, 1113]], "bbox": [166, 729, 1309, 1113], "points": [[167, 729], [167, 788], [166, 789], [167, 790], [167, 792], [166, 794], [167, 795], [167, 1082], [166, 1084], [167, 1085], [167, 1113], [1308, 1113], [1309, 1112], [1309, 1107], [1308, 1106], [1308, 1101], [1309, 1100], [1308, 1099], [1309, 1098], [1308, 1096], [1309, 1095], [1309, 1075], [1308, 1074], [1308, 1073], [1309, 1072], [1308, 1071], [1309, 1070], [1309, 1056], [1308, 1054], [1309, 1053], [1308, 1052], [1309, 1051], [1309, 1044], [1308, 1043], [1309, 1042], [1308, 1040], [1309, 1039], [1308, 1038], [1309, 1037], [1309, 1020], [1308, 1019], [1309, 1018], [1309, 1011], [1308, 1010], [1309, 1009], [1309, 950], [1308, 949], [1309, 948], [1308, 947], [1309, 946], [1309, 929], [1308, 928], [1309, 927], [1308, 926], [1309, 925], [1309, 919], [1308, 918], [1308, 916], [1309, 915], [1309, 897], [1308, 895], [1309, 894], [1309, 887], [1308, 886], [1309, 885], [1308, 884], [1309, 882], [1308, 881], [1309, 880], [1309, 864], [1308, 863], [1309, 861], [1309, 856], [1308, 854], [1308, 853], [1309, 852], [1309, 836], [1308, 835], [1309, 833], [1308, 832], [1309, 831], [1309, 824], [1308, 823], [1309, 822], [1308, 820], [1309, 819], [1309, 792], [1308, 791], [1308, 790], [1309, 789], [1309, 773], [1308, 771], [1308, 769], [1309, 768], [1309, 761], [1308, 760], [1308, 736], [1309, 735], [1309, 730], [171, 730], [170, 729]], "type": "table", "contains": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84]}, {"location": [[1213, 549], [1298, 549], [1298, 576], [1213, 576]], "bbox": [1213, 549, 1298, 576], "points": [[1213, 550], [1215, 549], [1296, 549], [1298, 550], [1298, 575], [1296, 576], [1215, 576], [1213, 575]], "type": "cell", "rows": [10, 10], "columns": [10, 10], "text_list": []}, {"location": [[1123, 549], [1209, 549], [1209, 576], [1123, 576]], "bbox": [1123, 549, 1209, 576], "points": [[1123, 550], [1125, 549], [1208, 549], [1209, 550], [1209, 575], [1208, 576], [1125, 576], [1123, 575]], "type": "cell", "rows": [10, 10], "columns": [9, 9], "text_list": []}, {"location": [[1032, 549], [1118, 549], [1118, 576], [1032, 576]], "bbox": [1032, 549, 1118, 576], "points": [[1035, 549], [1116, 549], [1118, 550], [1118, 575], [1116, 576], [1035, 576], [1032, 574], [1032, 568], [1033, 567], [1032, 566], [1033, 564], [1032, 563], [1033, 562], [1032, 561], [1033, 560], [1032, 559], [1033, 557], [1033, 553], [1032, 552]], "type": "cell", "rows": [10, 10], "columns": [8, 8], "text_list": []}, {"location": [[942, 549], [1027, 549], [1027, 576], [942, 576]], "bbox": [942, 549, 1027, 576], "points": [[942, 550], [943, 549], [1026, 549], [1027, 550], [1027, 575], [1026, 576], [943, 576], [942, 575]], "type": "cell", "rows": [10, 10], "columns": [7, 7], "text_list": []}, {"location": [[852, 549], [937, 549], [937, 576], [852, 576]], "bbox": [852, 549, 937, 576], "points": [[853, 550], [854, 549], [936, 549], [937, 550], [937, 575], [936, 576], [854, 576], [852, 574], [853, 573]], "type": "cell", "rows": [10, 10], "columns": [6, 6], "text_list": []}, {"location": [[762, 549], [847, 549], [847, 576], [762, 576]], "bbox": [762, 549, 847, 576], "points": [[763, 550], [764, 549], [846, 549], [847, 550], [847, 575], [846, 576], [763, 576], [762, 575], [763, 574]], "type": "cell", "rows": [10, 10], "columns": [5, 5], "text_list": []}, {"location": [[673, 549], [759, 549], [759, 576], [673, 576]], "bbox": [673, 549, 759, 576], "points": [[673, 550], [674, 549], [756, 549], [759, 552], [757, 553], [759, 554], [759, 574], [756, 576], [674, 576], [673, 575]], "type": "cell", "rows": [10, 10], "columns": [4, 4], "text_list": []}, {"location": [[233, 549], [569, 549], [569, 576], [233, 576]], "bbox": [233, 549, 569, 576], "points": [[235, 550], [236, 549], [568, 549], [569, 550], [569, 575], [568, 576], [235, 576], [233, 575], [235, 574]], "type": "cell", "rows": [10, 10], "columns": [1, 2], "text_list": []}, {"location": [[1213, 519], [1298, 519], [1298, 544], [1213, 544]], "bbox": [1213, 519, 1298, 544], "points": [[1213, 520], [1215, 519], [1296, 519], [1298, 520], [1298, 543], [1296, 544], [1215, 544], [1213, 543]], "type": "cell", "rows": [9, 9], "columns": [10, 10], "text_list": []}, {"location": [[1123, 519], [1209, 519], [1209, 544], [1123, 544]], "bbox": [1123, 519, 1209, 544], "points": [[1123, 520], [1125, 519], [1208, 519], [1209, 520], [1209, 543], [1208, 544], [1125, 544], [1123, 543]], "type": "cell", "rows": [9, 9], "columns": [9, 9], "text_list": []}, {"location": [[1032, 519], [1118, 519], [1118, 544], [1032, 544]], "bbox": [1032, 519, 1118, 544], "points": [[1035, 519], [1116, 519], [1118, 520], [1118, 543], [1116, 544], [1035, 544], [1032, 542], [1033, 541], [1033, 527], [1032, 526], [1033, 525], [1032, 523], [1033, 522], [1032, 521]], "type": "cell", "rows": [9, 9], "columns": [8, 8], "text_list": []}, {"location": [[942, 519], [1027, 519], [1027, 544], [942, 544]], "bbox": [942, 519, 1027, 544], "points": [[942, 520], [943, 519], [1026, 519], [1027, 520], [1027, 543], [1026, 544], [944, 544], [942, 542], [943, 541], [942, 540], [943, 539], [942, 537], [943, 536], [942, 535], [943, 534], [942, 533]], "type": "cell", "rows": [9, 9], "columns": [7, 7], "text_list": []}, {"location": [[853, 519], [937, 519], [937, 544], [853, 544]], "bbox": [853, 519, 937, 544], "points": [[853, 520], [854, 519], [936, 519], [937, 520], [937, 543], [936, 544], [854, 544], [853, 543]], "type": "cell", "rows": [9, 9], "columns": [6, 6], "text_list": []}, {"location": [[763, 519], [847, 519], [847, 544], [763, 544]], "bbox": [763, 519, 847, 544], "points": [[763, 520], [764, 519], [846, 519], [847, 520], [847, 543], [846, 544], [764, 544], [763, 543]], "type": "cell", "rows": [9, 9], "columns": [5, 5], "text_list": []}, {"location": [[673, 519], [759, 519], [759, 544], [673, 544]], "bbox": [673, 519, 759, 544], "points": [[673, 520], [674, 519], [756, 519], [759, 521], [757, 522], [759, 523], [759, 540], [757, 541], [757, 543], [756, 544], [674, 544], [673, 543]], "type": "cell", "rows": [9, 9], "columns": [4, 4], "text_list": []}, {"location": [[383, 519], [569, 519], [569, 544], [383, 544]], "bbox": [383, 519, 569, 544], "points": [[383, 520], [384, 519], [568, 519], [569, 520], [569, 543], [568, 544], [384, 544], [383, 543]], "type": "cell", "rows": [9, 9], "columns": [2, 2], "text_list": []}, {"location": [[233, 519], [377, 519], [377, 544], [233, 544]], "bbox": [233, 519, 377, 544], "points": [[233, 520], [235, 519], [376, 519], [377, 520], [377, 543], [376, 544], [236, 544], [235, 543], [235, 540], [233, 539], [233, 537], [235, 536], [233, 535], [233, 523], [235, 522], [233, 521]], "type": "cell", "rows": [9, 9], "columns": [1, 1], "text_list": []}, {"location": [[1213, 488], [1298, 488], [1298, 515], [1213, 515]], "bbox": [1213, 488, 1298, 515], "points": [[1213, 490], [1215, 488], [1216, 490], [1218, 490], [1219, 488], [1220, 490], [1222, 488], [1296, 488], [1298, 490], [1298, 514], [1296, 515], [1215, 515], [1213, 514]], "type": "cell", "rows": [8, 8], "columns": [10, 10], "text_list": []}, {"location": [[1123, 488], [1209, 488], [1209, 515], [1123, 515]], "bbox": [1123, 488, 1209, 515], "points": [[1123, 490], [1125, 488], [1126, 490], [1132, 490], [1133, 488], [1194, 488], [1195, 490], [1196, 488], [1197, 490], [1198, 488], [1199, 490], [1202, 490], [1203, 488], [1204, 490], [1205, 488], [1206, 490], [1208, 490], [1209, 491], [1209, 514], [1208, 515], [1125, 515], [1123, 514]], "type": "cell", "rows": [8, 8], "columns": [9, 9], "text_list": []}, {"location": [[1032, 488], [1118, 488], [1118, 515], [1032, 515]], "bbox": [1032, 488, 1118, 515], "points": [[1035, 488], [1036, 490], [1037, 488], [1038, 490], [1039, 488], [1040, 490], [1042, 488], [1043, 490], [1044, 488], [1107, 488], [1108, 490], [1109, 488], [1111, 490], [1112, 488], [1116, 488], [1118, 490], [1118, 514], [1116, 515], [1035, 515], [1033, 514], [1033, 513], [1032, 512], [1033, 511], [1032, 509], [1033, 508], [1033, 497], [1032, 495], [1033, 494], [1032, 493], [1032, 491]], "type": "cell", "rows": [8, 8], "columns": [8, 8], "text_list": []}, {"location": [[942, 488], [1027, 488], [1027, 515], [942, 515]], "bbox": [942, 488, 1027, 515], "points": [[942, 490], [943, 488], [944, 488], [946, 490], [949, 490], [950, 488], [951, 490], [953, 488], [1016, 488], [1017, 490], [1018, 488], [1019, 490], [1020, 488], [1022, 490], [1026, 490], [1027, 491], [1027, 514], [1026, 515], [943, 515], [942, 514], [943, 513], [942, 512]], "type": "cell", "rows": [8, 8], "columns": [7, 7], "text_list": []}, {"location": [[853, 488], [937, 488], [937, 515], [853, 515]], "bbox": [853, 488, 937, 515], "points": [[853, 490], [854, 488], [856, 490], [860, 490], [861, 488], [863, 490], [864, 488], [929, 488], [930, 490], [932, 488], [933, 490], [934, 488], [935, 490], [936, 490], [937, 491], [937, 514], [936, 515], [854, 515], [853, 514]], "type": "cell", "rows": [8, 8], "columns": [6, 6], "text_list": []}, {"location": [[762, 488], [847, 488], [847, 515], [762, 515]], "bbox": [762, 488, 847, 515], "points": [[763, 490], [764, 488], [766, 490], [771, 490], [773, 488], [774, 490], [775, 488], [838, 488], [839, 490], [840, 488], [842, 490], [843, 488], [844, 490], [845, 488], [847, 491], [847, 514], [846, 515], [763, 515], [762, 514], [763, 513]], "type": "cell", "rows": [8, 8], "columns": [5, 5], "text_list": []}, {"location": [[673, 488], [759, 488], [759, 515], [673, 515]], "bbox": [673, 488, 759, 515], "points": [[673, 490], [674, 488], [679, 488], [680, 490], [681, 488], [682, 490], [684, 488], [751, 488], [753, 490], [754, 488], [755, 490], [757, 490], [759, 491], [759, 511], [757, 512], [757, 514], [756, 515], [674, 515], [673, 514]], "type": "cell", "rows": [8, 8], "columns": [4, 4], "text_list": []}, {"location": [[383, 488], [569, 488], [569, 515], [383, 515]], "bbox": [383, 488, 569, 515], "points": [[383, 490], [384, 488], [475, 488], [477, 490], [478, 488], [479, 490], [480, 488], [481, 490], [483, 488], [484, 490], [485, 488], [486, 490], [487, 488], [488, 490], [490, 488], [491, 490], [492, 488], [501, 488], [502, 490], [504, 488], [505, 490], [506, 488], [507, 490], [508, 488], [509, 490], [511, 488], [512, 490], [513, 488], [550, 488], [552, 490], [553, 488], [554, 490], [555, 488], [556, 490], [557, 488], [567, 488], [569, 491], [569, 514], [568, 515], [384, 515], [383, 514]], "type": "cell", "rows": [8, 8], "columns": [2, 2], "text_list": []}, {"location": [[233, 488], [377, 488], [377, 515], [233, 515]], "bbox": [233, 488, 377, 515], "points": [[233, 490], [235, 488], [356, 488], [357, 490], [359, 488], [360, 490], [361, 488], [362, 490], [363, 488], [364, 490], [366, 488], [367, 490], [368, 488], [369, 490], [370, 488], [371, 490], [373, 488], [374, 490], [375, 488], [376, 488], [377, 490], [377, 514], [376, 515], [236, 515], [235, 514], [235, 511], [233, 509]], "type": "cell", "rows": [8, 8], "columns": [1, 1], "text_list": []}, {"location": [[1212, 457], [1298, 457], [1298, 484], [1212, 484]], "bbox": [1212, 457, 1298, 484], "points": [[1212, 458], [1213, 457], [1296, 457], [1298, 458], [1298, 483], [1296, 484], [1215, 484], [1213, 483], [1213, 459]], "type": "cell", "rows": [7, 7], "columns": [10, 10], "text_list": []}, {"location": [[1123, 457], [1209, 457], [1209, 484], [1123, 484]], "bbox": [1123, 457, 1209, 484], "points": [[1123, 458], [1125, 457], [1208, 457], [1209, 458], [1209, 483], [1208, 484], [1125, 484], [1123, 483]], "type": "cell", "rows": [7, 7], "columns": [9, 9], "text_list": []}, {"location": [[1032, 457], [1118, 457], [1118, 484], [1032, 484]], "bbox": [1032, 457, 1118, 484], "points": [[1032, 458], [1033, 457], [1116, 457], [1118, 458], [1118, 483], [1116, 484], [1033, 484], [1032, 483], [1032, 479], [1033, 478], [1032, 477], [1033, 475], [1032, 474], [1033, 473], [1032, 472], [1033, 471], [1032, 470], [1033, 468], [1032, 467]], "type": "cell", "rows": [7, 7], "columns": [8, 8], "text_list": []}, {"location": [[942, 457], [1027, 457], [1027, 484], [942, 484]], "bbox": [942, 457, 1027, 484], "points": [[942, 458], [943, 457], [1026, 457], [1027, 458], [1027, 483], [1026, 484], [943, 484], [942, 483]], "type": "cell", "rows": [7, 7], "columns": [7, 7], "text_list": []}, {"location": [[852, 457], [937, 457], [937, 484], [852, 484]], "bbox": [852, 457, 937, 484], "points": [[852, 458], [853, 457], [936, 457], [937, 458], [937, 483], [936, 484], [854, 484], [853, 483], [853, 461], [852, 460]], "type": "cell", "rows": [7, 7], "columns": [6, 6], "text_list": []}, {"location": [[762, 457], [847, 457], [847, 484], [762, 484]], "bbox": [762, 457, 847, 484], "points": [[762, 458], [763, 457], [846, 457], [847, 458], [847, 483], [846, 484], [764, 484], [763, 483], [763, 459]], "type": "cell", "rows": [7, 7], "columns": [5, 5], "text_list": []}, {"location": [[673, 457], [759, 457], [759, 484], [673, 484]], "bbox": [673, 457, 759, 484], "points": [[673, 458], [674, 457], [757, 457], [759, 458], [759, 483], [757, 484], [674, 484], [673, 483]], "type": "cell", "rows": [7, 7], "columns": [4, 4], "text_list": []}, {"location": [[383, 457], [569, 457], [569, 484], [383, 484]], "bbox": [383, 457, 569, 484], "points": [[383, 458], [384, 457], [568, 457], [569, 458], [569, 483], [568, 484], [384, 484], [383, 483]], "type": "cell", "rows": [7, 7], "columns": [2, 2], "text_list": []}, {"location": [[233, 457], [377, 457], [377, 484], [233, 484]], "bbox": [233, 457, 377, 484], "points": [[233, 458], [235, 457], [376, 457], [377, 458], [377, 483], [376, 484], [236, 484], [235, 483], [235, 475], [233, 474], [233, 460], [235, 459]], "type": "cell", "rows": [7, 7], "columns": [1, 1], "text_list": []}, {"location": [[1213, 426], [1298, 426], [1298, 452], [1213, 452]], "bbox": [1213, 426, 1298, 452], "points": [[1213, 428], [1215, 426], [1296, 426], [1298, 428], [1298, 451], [1296, 452], [1215, 452], [1213, 451]], "type": "cell", "rows": [6, 6], "columns": [10, 10], "text_list": []}, {"location": [[1123, 426], [1209, 426], [1209, 452], [1123, 452]], "bbox": [1123, 426, 1209, 452], "points": [[1123, 428], [1125, 426], [1206, 426], [1209, 429], [1209, 451], [1208, 452], [1125, 452], [1123, 451]], "type": "cell", "rows": [6, 6], "columns": [9, 9], "text_list": []}, {"location": [[1032, 426], [1118, 426], [1118, 452], [1032, 452]], "bbox": [1032, 426, 1118, 452], "points": [[1032, 428], [1033, 426], [1116, 426], [1118, 428], [1118, 451], [1116, 452], [1035, 452], [1032, 450]], "type": "cell", "rows": [6, 6], "columns": [8, 8], "text_list": []}, {"location": [[942, 426], [1027, 426], [1027, 452], [942, 452]], "bbox": [942, 426, 1027, 452], "points": [[942, 428], [943, 426], [1026, 426], [1027, 428], [1027, 451], [1026, 452], [943, 452], [942, 451]], "type": "cell", "rows": [6, 6], "columns": [7, 7], "text_list": []}, {"location": [[852, 426], [937, 426], [937, 452], [852, 452]], "bbox": [852, 426, 937, 452], "points": [[852, 428], [853, 426], [936, 426], [937, 428], [937, 451], [936, 452], [854, 452], [853, 451], [853, 431], [852, 430], [853, 429]], "type": "cell", "rows": [6, 6], "columns": [6, 6], "text_list": []}, {"location": [[762, 426], [847, 426], [847, 452], [762, 452]], "bbox": [762, 426, 847, 452], "points": [[762, 428], [763, 426], [846, 426], [847, 428], [847, 451], [846, 452], [764, 452], [763, 451], [763, 429]], "type": "cell", "rows": [6, 6], "columns": [5, 5], "text_list": []}, {"location": [[673, 426], [759, 426], [759, 452], [673, 452]], "bbox": [673, 426, 759, 452], "points": [[673, 428], [674, 426], [756, 426], [759, 429], [759, 451], [757, 452], [674, 452], [673, 451]], "type": "cell", "rows": [6, 6], "columns": [4, 4], "text_list": []}, {"location": [[383, 426], [569, 426], [569, 452], [383, 452]], "bbox": [383, 426, 569, 452], "points": [[383, 428], [384, 426], [568, 426], [569, 428], [569, 451], [568, 452], [384, 452], [383, 451]], "type": "cell", "rows": [6, 6], "columns": [2, 2], "text_list": []}, {"location": [[233, 426], [377, 426], [377, 452], [233, 452]], "bbox": [233, 426, 377, 452], "points": [[233, 428], [235, 426], [376, 426], [377, 428], [377, 451], [376, 452], [236, 452], [233, 450]], "type": "cell", "rows": [6, 6], "columns": [1, 1], "text_list": []}, {"location": [[1213, 395], [1298, 395], [1298, 422], [1213, 422]], "bbox": [1213, 395, 1298, 422], "points": [[1244, 396], [1245, 395], [1273, 395], [1274, 396], [1275, 395], [1277, 396], [1296, 396], [1298, 397], [1298, 421], [1296, 422], [1215, 422], [1213, 421], [1213, 397], [1215, 396]], "type": "cell", "rows": [5, 5], "columns": [10, 10], "text_list": []}, {"location": [[1123, 395], [1209, 395], [1209, 422], [1123, 422]], "bbox": [1123, 395, 1209, 422], "points": [[1153, 396], [1154, 395], [1180, 395], [1181, 396], [1208, 396], [1209, 397], [1209, 421], [1208, 422], [1125, 422], [1123, 421], [1123, 397], [1125, 396]], "type": "cell", "rows": [5, 5], "columns": [9, 9], "text_list": []}, {"location": [[1032, 395], [1118, 395], [1118, 422], [1032, 422]], "bbox": [1032, 395, 1118, 422], "points": [[1064, 396], [1065, 395], [1088, 395], [1089, 396], [1091, 395], [1092, 396], [1116, 396], [1118, 397], [1118, 421], [1116, 422], [1035, 422], [1032, 419], [1032, 414], [1033, 412], [1032, 411], [1033, 410], [1032, 409], [1033, 408], [1032, 406], [1032, 397], [1033, 396]], "type": "cell", "rows": [5, 5], "columns": [8, 8], "text_list": []}, {"location": [[942, 395], [1027, 395], [1027, 422], [942, 422]], "bbox": [942, 395, 1027, 422], "points": [[973, 396], [974, 395], [997, 395], [998, 396], [999, 395], [1001, 396], [1026, 396], [1027, 397], [1027, 421], [1026, 422], [943, 422], [942, 421], [942, 397], [943, 396]], "type": "cell", "rows": [5, 5], "columns": [7, 7], "text_list": []}, {"location": [[852, 395], [937, 395], [937, 422], [852, 422]], "bbox": [852, 395, 937, 422], "points": [[884, 396], [885, 395], [911, 395], [912, 396], [913, 395], [914, 396], [936, 396], [937, 397], [937, 421], [936, 422], [854, 422], [853, 421], [853, 398], [852, 397], [853, 396]], "type": "cell", "rows": [5, 5], "columns": [6, 6], "text_list": []}, {"location": [[762, 395], [847, 395], [847, 422], [762, 422]], "bbox": [762, 395, 847, 422], "points": [[792, 396], [794, 395], [819, 395], [820, 396], [822, 395], [823, 396], [846, 396], [847, 397], [847, 421], [846, 422], [763, 422], [762, 421], [763, 419], [763, 398], [762, 397], [763, 396]], "type": "cell", "rows": [5, 5], "columns": [5, 5], "text_list": []}, {"location": [[673, 395], [759, 395], [759, 422], [673, 422]], "bbox": [673, 395, 759, 422], "points": [[673, 396], [674, 395], [675, 396], [701, 396], [702, 395], [737, 395], [739, 396], [757, 396], [759, 397], [759, 419], [756, 422], [674, 422], [673, 421]], "type": "cell", "rows": [5, 5], "columns": [4, 4], "text_list": []}, {"location": [[383, 395], [569, 395], [569, 422], [383, 422]], "bbox": [383, 395, 569, 422], "points": [[383, 396], [384, 395], [394, 395], [395, 396], [396, 395], [397, 396], [402, 396], [403, 395], [404, 396], [405, 396], [406, 395], [422, 395], [423, 396], [444, 396], [445, 395], [457, 395], [458, 396], [459, 395], [460, 396], [465, 396], [466, 395], [471, 395], [472, 396], [473, 395], [474, 396], [477, 396], [478, 395], [480, 395], [481, 396], [483, 395], [487, 395], [488, 396], [568, 396], [569, 397], [569, 421], [568, 422], [384, 422], [383, 421]], "type": "cell", "rows": [5, 5], "columns": [2, 2], "text_list": []}, {"location": [[1213, 364], [1298, 364], [1298, 391], [1213, 391]], "bbox": [1213, 364, 1298, 391], "points": [[1213, 366], [1215, 364], [1216, 366], [1217, 364], [1296, 364], [1298, 366], [1298, 390], [1296, 391], [1215, 391], [1213, 390]], "type": "cell", "rows": [4, 4], "columns": [10, 10], "text_list": []}, {"location": [[1123, 364], [1209, 364], [1209, 391], [1123, 391]], "bbox": [1123, 364, 1209, 391], "points": [[1123, 366], [1125, 364], [1129, 364], [1130, 366], [1132, 364], [1206, 364], [1209, 367], [1209, 390], [1208, 391], [1125, 391], [1123, 390]], "type": "cell", "rows": [4, 4], "columns": [9, 9], "text_list": []}, {"location": [[1032, 364], [1118, 364], [1118, 391], [1032, 391]], "bbox": [1032, 364, 1118, 391], "points": [[1035, 364], [1036, 366], [1037, 364], [1116, 364], [1118, 366], [1118, 390], [1116, 391], [1035, 391], [1032, 389], [1032, 385], [1033, 384], [1032, 383], [1033, 382], [1032, 381], [1033, 380], [1033, 375], [1032, 374], [1033, 373], [1032, 371], [1033, 370], [1032, 369], [1032, 367]], "type": "cell", "rows": [4, 4], "columns": [8, 8], "text_list": []}, {"location": [[942, 364], [1027, 364], [1027, 391], [942, 391]], "bbox": [942, 364, 1027, 391], "points": [[942, 366], [943, 364], [944, 364], [946, 366], [947, 364], [1023, 364], [1024, 366], [1025, 364], [1026, 364], [1027, 366], [1027, 390], [1026, 391], [943, 391], [942, 390]], "type": "cell", "rows": [4, 4], "columns": [7, 7], "text_list": []}, {"location": [[852, 364], [937, 364], [937, 391], [852, 391]], "bbox": [852, 364, 937, 391], "points": [[853, 366], [854, 364], [856, 366], [857, 364], [934, 364], [935, 366], [936, 364], [937, 366], [937, 390], [936, 391], [854, 391], [852, 389], [853, 388]], "type": "cell", "rows": [4, 4], "columns": [6, 6], "text_list": []}, {"location": [[762, 364], [847, 364], [847, 391], [762, 391]], "bbox": [762, 364, 847, 391], "points": [[762, 366], [763, 364], [764, 364], [766, 366], [767, 364], [846, 364], [847, 366], [847, 390], [846, 391], [763, 391], [762, 390], [763, 389], [763, 367]], "type": "cell", "rows": [4, 4], "columns": [5, 5], "text_list": []}, {"location": [[673, 364], [759, 364], [759, 391], [673, 391]], "bbox": [673, 364, 759, 391], "points": [[673, 366], [674, 364], [756, 364], [759, 367], [759, 389], [756, 391], [674, 391], [673, 390]], "type": "cell", "rows": [4, 4], "columns": [4, 4], "text_list": []}, {"location": [[383, 364], [569, 364], [569, 391], [383, 391]], "bbox": [383, 364, 569, 391], "points": [[383, 366], [384, 364], [567, 364], [569, 367], [569, 390], [568, 391], [384, 391], [383, 390]], "type": "cell", "rows": [4, 4], "columns": [2, 2], "text_list": []}, {"location": [[233, 364], [377, 364], [377, 422], [233, 422]], "bbox": [233, 364, 377, 422], "points": [[235, 366], [236, 364], [239, 364], [240, 366], [242, 364], [243, 366], [244, 364], [245, 366], [246, 364], [247, 366], [249, 364], [250, 366], [251, 364], [252, 366], [253, 364], [254, 366], [259, 366], [260, 364], [261, 366], [263, 364], [264, 366], [265, 364], [266, 366], [267, 364], [268, 366], [270, 364], [271, 366], [272, 364], [273, 366], [274, 364], [276, 366], [277, 364], [278, 366], [279, 364], [280, 366], [374, 366], [375, 364], [376, 364], [377, 366], [377, 421], [376, 422], [236, 422], [235, 421], [235, 396], [233, 395], [235, 394], [233, 392], [233, 390], [235, 389], [233, 388], [233, 383], [235, 382]], "type": "cell", "rows": [4, 5], "columns": [1, 1], "text_list": []}, {"location": [[1213, 334], [1298, 334], [1298, 360], [1213, 360]], "bbox": [1213, 334, 1298, 360], "points": [[1213, 335], [1215, 334], [1296, 334], [1298, 335], [1298, 359], [1296, 360], [1215, 360], [1213, 359]], "type": "cell", "rows": [3, 3], "columns": [10, 10], "text_list": []}, {"location": [[1123, 334], [1209, 334], [1209, 360], [1123, 360]], "bbox": [1123, 334, 1209, 360], "points": [[1123, 335], [1125, 334], [1208, 334], [1209, 335], [1209, 359], [1208, 360], [1125, 360], [1123, 359]], "type": "cell", "rows": [3, 3], "columns": [9, 9], "text_list": []}, {"location": [[1032, 334], [1118, 334], [1118, 360], [1032, 360]], "bbox": [1032, 334, 1118, 360], "points": [[1032, 335], [1033, 334], [1116, 334], [1118, 335], [1118, 359], [1116, 360], [1033, 360], [1032, 359], [1032, 355], [1033, 354], [1032, 353], [1033, 352], [1032, 350], [1033, 349], [1032, 348], [1033, 347], [1033, 345], [1032, 343], [1033, 342], [1032, 341]], "type": "cell", "rows": [3, 3], "columns": [8, 8], "text_list": []}, {"location": [[942, 334], [1027, 334], [1027, 360], [942, 360]], "bbox": [942, 334, 1027, 360], "points": [[942, 335], [943, 334], [1026, 334], [1027, 335], [1027, 359], [1026, 360], [943, 360], [942, 359]], "type": "cell", "rows": [3, 3], "columns": [7, 7], "text_list": []}, {"location": [[852, 334], [937, 334], [937, 360], [852, 360]], "bbox": [852, 334, 937, 360], "points": [[854, 334], [936, 334], [937, 335], [937, 359], [936, 360], [854, 360], [853, 359], [853, 337], [852, 336]], "type": "cell", "rows": [3, 3], "columns": [6, 6], "text_list": []}, {"location": [[762, 334], [847, 334], [847, 360], [762, 360]], "bbox": [762, 334, 847, 360], "points": [[762, 335], [763, 334], [846, 334], [847, 335], [847, 359], [846, 360], [764, 360], [763, 359], [763, 336]], "type": "cell", "rows": [3, 3], "columns": [5, 5], "text_list": []}, {"location": [[673, 334], [759, 334], [759, 360], [673, 360]], "bbox": [673, 334, 759, 360], "points": [[673, 335], [674, 334], [756, 334], [759, 336], [759, 359], [757, 360], [674, 360], [673, 359]], "type": "cell", "rows": [3, 3], "columns": [4, 4], "text_list": []}, {"location": [[383, 334], [569, 334], [569, 360], [383, 360]], "bbox": [383, 334, 569, 360], "points": [[383, 335], [384, 334], [568, 334], [569, 335], [569, 359], [568, 360], [384, 360], [383, 359]], "type": "cell", "rows": [3, 3], "columns": [2, 2], "text_list": []}, {"location": [[233, 334], [377, 334], [377, 360], [233, 360]], "bbox": [233, 334, 377, 360], "points": [[233, 335], [235, 334], [376, 334], [377, 335], [377, 359], [376, 360], [236, 360], [235, 359], [235, 356], [233, 355], [233, 339], [235, 337], [233, 336]], "type": "cell", "rows": [3, 3], "columns": [1, 1], "text_list": []}, {"location": [[1213, 302], [1298, 302], [1298, 329], [1213, 329]], "bbox": [1213, 302, 1298, 329], "points": [[1213, 304], [1215, 302], [1296, 302], [1298, 304], [1298, 328], [1296, 329], [1215, 329], [1213, 328]], "type": "cell", "rows": [2, 2], "columns": [10, 10], "text_list": []}, {"location": [[1123, 302], [1209, 302], [1209, 329], [1123, 329]], "bbox": [1123, 302, 1209, 329], "points": [[1123, 304], [1125, 302], [1206, 302], [1209, 305], [1209, 328], [1208, 329], [1125, 329], [1123, 328]], "type": "cell", "rows": [2, 2], "columns": [9, 9], "text_list": []}, {"location": [[1032, 302], [1118, 302], [1118, 329], [1032, 329]], "bbox": [1032, 302, 1118, 329], "points": [[1032, 304], [1033, 302], [1116, 302], [1118, 304], [1118, 328], [1116, 329], [1035, 329], [1032, 327], [1032, 325], [1033, 323], [1032, 322], [1033, 321], [1033, 314], [1032, 313], [1033, 312], [1032, 311], [1033, 309], [1032, 308]], "type": "cell", "rows": [2, 2], "columns": [8, 8], "text_list": []}, {"location": [[942, 302], [1027, 302], [1027, 329], [942, 329]], "bbox": [942, 302, 1027, 329], "points": [[942, 304], [943, 302], [1026, 302], [1027, 304], [1027, 328], [1026, 329], [944, 329], [942, 327]], "type": "cell", "rows": [2, 2], "columns": [7, 7], "text_list": []}, {"location": [[852, 302], [937, 302], [937, 329], [852, 329]], "bbox": [852, 302, 937, 329], "points": [[852, 304], [853, 302], [936, 302], [937, 304], [937, 328], [936, 329], [854, 329], [853, 328], [853, 305]], "type": "cell", "rows": [2, 2], "columns": [6, 6], "text_list": []}, {"location": [[762, 302], [847, 302], [847, 329], [762, 329]], "bbox": [762, 302, 847, 329], "points": [[762, 304], [763, 302], [846, 302], [847, 304], [847, 328], [846, 329], [764, 329], [763, 328], [763, 305]], "type": "cell", "rows": [2, 2], "columns": [5, 5], "text_list": []}, {"location": [[673, 302], [759, 302], [759, 329], [673, 329]], "bbox": [673, 302, 759, 329], "points": [[673, 304], [674, 302], [756, 302], [759, 305], [759, 328], [757, 329], [674, 329], [673, 328]], "type": "cell", "rows": [2, 2], "columns": [4, 4], "text_list": []}, {"location": [[383, 302], [569, 302], [569, 329], [383, 329]], "bbox": [383, 302, 569, 329], "points": [[383, 304], [384, 302], [568, 302], [569, 304], [569, 328], [568, 329], [384, 329], [383, 328]], "type": "cell", "rows": [2, 2], "columns": [2, 2], "text_list": []}, {"location": [[1213, 273], [1298, 273], [1298, 298], [1213, 298]], "bbox": [1213, 273, 1298, 298], "points": [[1213, 274], [1215, 273], [1296, 273], [1298, 274], [1298, 297], [1296, 298], [1215, 298], [1213, 297]], "type": "cell", "rows": [1, 1], "columns": [10, 10], "text_list": []}, {"location": [[1123, 273], [1209, 273], [1209, 298], [1123, 298]], "bbox": [1123, 273, 1209, 298], "points": [[1123, 274], [1125, 273], [1208, 273], [1209, 274], [1209, 297], [1208, 298], [1125, 298], [1123, 297]], "type": "cell", "rows": [1, 1], "columns": [9, 9], "text_list": []}, {"location": [[1032, 273], [1118, 273], [1118, 298], [1032, 298]], "bbox": [1032, 273, 1118, 298], "points": [[1032, 274], [1033, 273], [1116, 273], [1118, 274], [1118, 297], [1116, 298], [1035, 298], [1032, 295]], "type": "cell", "rows": [1, 1], "columns": [8, 8], "text_list": []}, {"location": [[942, 273], [1027, 273], [1027, 298], [942, 298]], "bbox": [942, 273, 1027, 298], "points": [[942, 274], [943, 273], [1026, 273], [1027, 274], [1027, 297], [1026, 298], [943, 298], [942, 297]], "type": "cell", "rows": [1, 1], "columns": [7, 7], "text_list": []}, {"location": [[852, 273], [937, 273], [937, 298], [852, 298]], "bbox": [852, 273, 937, 298], "points": [[852, 274], [853, 273], [936, 273], [937, 274], [937, 297], [936, 298], [854, 298], [853, 297], [853, 281], [852, 280]], "type": "cell", "rows": [1, 1], "columns": [6, 6], "text_list": []}, {"location": [[762, 273], [847, 273], [847, 298], [762, 298]], "bbox": [762, 273, 847, 298], "points": [[762, 274], [763, 273], [846, 273], [847, 274], [847, 297], [846, 298], [763, 298], [762, 297], [763, 295], [763, 277], [762, 276]], "type": "cell", "rows": [1, 1], "columns": [5, 5], "text_list": []}, {"location": [[673, 273], [759, 273], [759, 298], [673, 298]], "bbox": [673, 273, 759, 298], "points": [[673, 274], [674, 273], [757, 273], [759, 274], [759, 295], [756, 298], [674, 298], [673, 297]], "type": "cell", "rows": [1, 1], "columns": [4, 4], "text_list": []}, {"location": [[573, 273], [668, 273], [668, 576], [573, 576]], "bbox": [573, 273, 668, 576], "points": [[574, 274], [575, 273], [667, 273], [668, 274], [668, 575], [667, 576], [574, 576], [573, 575], [574, 574], [574, 457], [575, 456], [575, 453], [574, 452]], "type": "cell", "rows": [1, 10], "columns": [3, 3], "text_list": []}, {"location": [[383, 273], [569, 273], [569, 298], [383, 298]], "bbox": [383, 273, 569, 298], "points": [[383, 274], [384, 273], [568, 273], [569, 274], [569, 297], [568, 298], [384, 298], [383, 297]], "type": "cell", "rows": [1, 1], "columns": [2, 2], "text_list": []}, {"location": [[233, 273], [377, 273], [377, 329], [233, 329]], "bbox": [233, 273, 377, 329], "points": [[233, 274], [235, 273], [376, 273], [377, 274], [377, 328], [376, 329], [236, 329], [235, 328], [235, 305], [233, 304], [235, 302], [233, 301], [233, 294], [235, 293], [233, 292], [235, 291], [233, 290], [235, 288], [235, 276]], "type": "cell", "rows": [1, 2], "columns": [1, 1], "text_list": []}, {"location": [[166, 272], [231, 272], [231, 576], [166, 576]], "bbox": [166, 272, 231, 576], "points": [[166, 273], [167, 272], [168, 273], [230, 273], [231, 274], [231, 278], [230, 279], [230, 329], [231, 330], [230, 332], [230, 339], [231, 340], [231, 353], [230, 354], [230, 360], [231, 361], [230, 362], [230, 385], [231, 387], [231, 392], [230, 394], [230, 430], [231, 431], [231, 459], [230, 460], [230, 464], [231, 465], [231, 467], [230, 468], [230, 484], [231, 485], [231, 486], [230, 487], [230, 488], [231, 490], [230, 491], [231, 492], [231, 508], [230, 509], [230, 526], [231, 527], [231, 534], [230, 535], [230, 571], [231, 573], [231, 575], [230, 576], [167, 576], [166, 575]], "type": "cell", "rows": [1, 10], "columns": [0, 0], "text_list": []}, {"location": [[1212, 240], [1298, 240], [1298, 267], [1212, 267]], "bbox": [1212, 240, 1298, 267], "points": [[1215, 240], [1216, 242], [1217, 240], [1296, 240], [1298, 242], [1298, 266], [1296, 267], [1215, 267], [1212, 265], [1213, 264], [1212, 263], [1212, 243]], "type": "cell", "rows": [0, 0], "columns": [10, 10], "text_list": []}, {"location": [[1123, 240], [1209, 240], [1209, 267], [1123, 267]], "bbox": [1123, 240, 1209, 267], "points": [[1123, 242], [1125, 240], [1126, 242], [1127, 242], [1128, 240], [1170, 240], [1171, 242], [1173, 240], [1183, 240], [1184, 242], [1185, 240], [1190, 240], [1191, 242], [1192, 240], [1208, 240], [1209, 242], [1209, 266], [1208, 267], [1125, 267], [1123, 266]], "type": "cell", "rows": [0, 0], "columns": [9, 9], "text_list": []}, {"location": [[1032, 240], [1118, 240], [1118, 267], [1032, 267]], "bbox": [1032, 240, 1118, 267], "points": [[1035, 240], [1036, 242], [1037, 240], [1038, 242], [1039, 240], [1116, 240], [1118, 242], [1118, 266], [1116, 267], [1035, 267], [1032, 265], [1032, 243]], "type": "cell", "rows": [0, 0], "columns": [8, 8], "text_list": []}, {"location": [[942, 240], [1027, 240], [1027, 267], [942, 267]], "bbox": [942, 240, 1027, 267], "points": [[942, 242], [943, 240], [944, 240], [946, 242], [947, 242], [948, 240], [1026, 240], [1027, 242], [1027, 266], [1026, 267], [943, 267], [942, 266]], "type": "cell", "rows": [0, 0], "columns": [7, 7], "text_list": []}, {"location": [[852, 240], [937, 240], [937, 267], [852, 267]], "bbox": [852, 240, 937, 267], "points": [[854, 240], [856, 242], [857, 240], [858, 242], [859, 240], [860, 242], [861, 240], [873, 240], [874, 242], [875, 240], [877, 242], [880, 242], [881, 240], [885, 240], [886, 242], [887, 240], [892, 240], [893, 242], [894, 240], [897, 240], [898, 242], [899, 240], [936, 240], [937, 242], [937, 266], [936, 267], [854, 267], [852, 265], [852, 243]], "type": "cell", "rows": [0, 0], "columns": [6, 6], "text_list": []}, {"location": [[762, 240], [847, 240], [847, 267], [762, 267]], "bbox": [762, 240, 847, 267], "points": [[762, 242], [763, 240], [764, 240], [766, 242], [767, 242], [768, 240], [769, 242], [770, 242], [771, 240], [781, 240], [782, 242], [785, 242], [787, 240], [788, 242], [790, 242], [791, 240], [795, 240], [796, 242], [797, 240], [803, 240], [804, 242], [805, 240], [846, 240], [847, 242], [847, 266], [846, 267], [763, 267], [762, 266]], "type": "cell", "rows": [0, 0], "columns": [5, 5], "text_list": []}, {"location": [[673, 240], [759, 240], [759, 267], [673, 267]], "bbox": [673, 240, 759, 267], "points": [[673, 242], [674, 240], [675, 242], [678, 242], [679, 240], [680, 242], [681, 240], [693, 240], [694, 242], [695, 240], [697, 242], [700, 242], [701, 240], [705, 240], [706, 242], [707, 240], [712, 240], [713, 242], [714, 240], [756, 240], [759, 243], [759, 265], [756, 267], [674, 267], [673, 266]], "type": "cell", "rows": [0, 0], "columns": [4, 4], "text_list": []}, {"location": [[573, 240], [668, 240], [668, 267], [573, 267]], "bbox": [573, 240, 668, 267], "points": [[601, 242], [602, 240], [630, 240], [631, 242], [632, 242], [633, 240], [637, 240], [638, 242], [667, 242], [668, 243], [668, 266], [667, 267], [574, 267], [573, 266], [573, 243], [574, 242]], "type": "cell", "rows": [0, 0], "columns": [3, 3], "text_list": []}, {"location": [[166, 240], [569, 240], [569, 267], [166, 267]], "bbox": [166, 240, 569, 267], "points": [[166, 242], [167, 240], [168, 242], [568, 242], [569, 243], [569, 266], [568, 267], [167, 267], [166, 266]], "type": "cell", "rows": [0, 0], "columns": [0, 2], "text_list": []}, {"location": [[161, 236], [1303, 236], [1303, 581], [161, 581]], "bbox": [161, 236, 1303, 581], "points": [[162, 236], [162, 238], [161, 239], [161, 580], [162, 581], [1302, 581], [1303, 580], [1303, 237], [166, 237], [164, 236]], "type": "table", "contains": [85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 142, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182]}]}}]