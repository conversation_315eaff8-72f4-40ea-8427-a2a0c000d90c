[build-system]
requires = ["setuptools >= 61.0", "wheel", "setuptools-git-versioning>=2.0,<3"]
build-backend = "setuptools.build_meta"

[tool.setuptools]
include-package-data = true
packages.find.exclude = ["ktem_tests*", "env*"]
packages.find.include = ["ktem*"]

[tool.setuptools-git-versioning]
enabled = true
dev_template = "{tag}"
dirty_template = "{tag}"
tag_filter = "v?\\d+(\\.\\d+)*.*"

[project]
name = "ktem"
dynamic = ["version"]
requires-python = ">= 3.10"
description = "RAG-based Question and Answering Application"
dependencies = [
    "click>=8.1.7,<9",
    "platformdirs>=4.2.1,<5",
    "pluggy>=1.5.0,<2",
    "python-decouple>=3.8,<4",
    "SQLAlchemy>=2.0.29,<3",
    "sqlmodel>=0.0.16,<0.1",
    "tiktoken>=0.6.0,<1",
    "gradio>=4.31.0,<5",
    "gradiologin",
    "python-multipart==0.0.12", # required for gradio, pinning to avoid yanking issues with micropip (fixed in gradio >= 5.4.0)
    "markdown>=3.6,<4",
    "tzlocal>=5.0",
]
authors = [
    { name = "@trducng", email = "<EMAIL>" },
    { name = "@lone17", email = "<EMAIL>" },
    { name = "@taprosoft", email = "<EMAIL>" },
    { name = "@cin-albert", email = "<EMAIL>" },
]
classifiers = [
    "Programming Language :: Python :: 3",
    "Operating System :: OS Independent",
]
